-- Add MFA support to staff_volunteers table
-- This script adds the necessary columns for MFA and biometric authentication

-- Add MFA-related columns to staff_volunteers table
ALTER TABLE public.staff_volunteers 
ADD COLUMN IF NOT EXISTS mfa_enabled boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS mfa_secret text,
ADD COLUMN IF NOT EXISTS mfa_backup_codes text[],
ADD COLUMN IF NOT EXISTS biometric_enabled boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS mfa_enrolled_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS last_mfa_verification timestamp with time zone;

-- Create table for WebAuthn credentials
CREATE TABLE IF NOT EXISTS public.webauthn_credentials (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  credential_id text NOT NULL UNIQUE,
  public_key text NOT NULL,
  counter bigint NOT NULL DEFAULT 0,
  device_type text NOT NULL DEFAULT 'unknown',
  device_name text,
  created_at timestamp with time zone DEFAULT now(),
  last_used_at timestamp with time zone,
  is_active boolean DEFAULT true,
  CONSTRAINT webauthn_credentials_pkey PRIMARY KEY (id),
  CONSTRAINT webauthn_credentials_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_webauthn_credentials_user_id ON public.webauthn_credentials(user_id);
CREATE INDEX IF NOT EXISTS idx_webauthn_credentials_credential_id ON public.webauthn_credentials(credential_id);

-- Enable RLS on webauthn_credentials table
ALTER TABLE public.webauthn_credentials ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for webauthn_credentials
CREATE POLICY "Users can view their own WebAuthn credentials" ON public.webauthn_credentials
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own WebAuthn credentials" ON public.webauthn_credentials
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own WebAuthn credentials" ON public.webauthn_credentials
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own WebAuthn credentials" ON public.webauthn_credentials
  FOR DELETE USING (auth.uid() = user_id);

-- Create table for MFA backup codes
CREATE TABLE IF NOT EXISTS public.mfa_backup_codes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  code_hash text NOT NULL,
  used_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT mfa_backup_codes_pkey PRIMARY KEY (id),
  CONSTRAINT mfa_backup_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create index for backup codes
CREATE INDEX IF NOT EXISTS idx_mfa_backup_codes_user_id ON public.mfa_backup_codes(user_id);

-- Enable RLS on mfa_backup_codes table
ALTER TABLE public.mfa_backup_codes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for mfa_backup_codes
CREATE POLICY "Users can view their own backup codes" ON public.mfa_backup_codes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own backup codes" ON public.mfa_backup_codes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own backup codes" ON public.mfa_backup_codes
  FOR UPDATE USING (auth.uid() = user_id);

-- Update user_preferences table to ensure MFA fields exist
ALTER TABLE public.user_preferences 
ADD COLUMN IF NOT EXISTS mfa_method text DEFAULT 'none' CHECK (mfa_method IN ('none', 'totp', 'sms', 'both'));

-- Add comment to document the schema changes
COMMENT ON COLUMN public.staff_volunteers.mfa_enabled IS 'Whether the user has MFA enabled';
COMMENT ON COLUMN public.staff_volunteers.mfa_secret IS 'Encrypted TOTP secret for the user';
COMMENT ON COLUMN public.staff_volunteers.mfa_backup_codes IS 'Array of backup codes for MFA recovery';
COMMENT ON COLUMN public.staff_volunteers.biometric_enabled IS 'Whether the user has biometric authentication enabled';
COMMENT ON COLUMN public.staff_volunteers.mfa_enrolled_at IS 'When the user first enrolled in MFA';
COMMENT ON COLUMN public.staff_volunteers.last_mfa_verification IS 'Last time the user verified with MFA';

COMMENT ON TABLE public.webauthn_credentials IS 'Stores WebAuthn credentials for biometric authentication';
COMMENT ON TABLE public.mfa_backup_codes IS 'Stores hashed backup codes for MFA recovery';
