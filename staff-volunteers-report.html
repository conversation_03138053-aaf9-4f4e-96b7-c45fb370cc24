<!DOCTYPE html>
<html lang="en">
<head>
 <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Staff & Volunteers Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    /* Report-specific styles */
    .report-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      background: white;
      min-height: 100vh;
    }

    .report-header {
      text-align: center;
      margin-bottom: 2rem;
      border-bottom: 2px solid #2c3e50;
      padding-bottom: 1rem;
    }

    .report-title {
      color: #2c3e50;
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .report-subtitle {
      color: #666;
      font-size: 1.1rem;
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1.5rem;
      text-align: center;
      border-left: 4px solid #4285a4;
    }

    .summary-number {
      font-size: 2rem;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }

    .summary-label {
      color: #666;
      font-size: 0.9rem;
    }

    .staff-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }

    .staff-card {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 1.5rem;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;
    }

    .staff-card:hover {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .staff-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .staff-photo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      object-position: top;
    }

    .staff-info h3 {
      margin: 0;
      color: #2c3e50;
      font-size: 1.1rem;
    }

    .staff-type {
      color: #666;
      font-size: 0.9rem;
      margin-top: 0.25rem;
    }

    .staff-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
      margin-top: 1rem;
    }

    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .detail-label {
      font-weight: 600;
      color: #2c3e50;
      font-size: 0.85rem;
    }

    .detail-value {
      color: #555;
      font-size: 0.9rem;
    }

    .status-active {
      color: #28a745;
      font-weight: 500;
    }

    .status-inactive {
      color: #dc3545;
      font-weight: 500;
    }

    .floating-buttons {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 1000;
    }

    .floating-btn {
      background: rgba(66, 133, 166, 0.9);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .close-btn {
      background: rgba(66, 133, 166, 0.9);
    }

    .close-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .no-data {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 2rem;
    }

    .loading {
      text-align: center;
      color: #666;
      padding: 2rem;
    }

    /* Print styles */
    @media print {
      .floating-buttons {
        display: none !important;
      }
      
      .report-container {
        padding: 0;
        box-shadow: none;
      }
      
      .staff-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .staff-card {
        page-break-inside: avoid;
        margin-bottom: 1rem;
      }
    }

    /* Responsive Design */

    /* Mobile (320px - 768px) */
    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      .report-container {
        padding: 1rem;
        margin: 0;
      }

      .report-title {
        font-size: 1.6rem;
      }

      .report-subtitle {
        font-size: 1rem;
      }

      .summary-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
      }

      .summary-card {
        padding: 1rem;
      }

      .summary-number {
        font-size: 1.6rem;
      }

      .summary-label {
        font-size: 0.8rem;
      }

      .staff-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .staff-card {
        padding: 1rem;
      }

      .staff-photo {
        width: 50px;
        height: 50px;
      }

      .staff-info h3 {
        font-size: 1rem;
      }

      .staff-details {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .floating-buttons {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-bottom: 1rem;
        gap: 8px;
      }

      .floating-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
      }
    }

    /* Tablet Portrait (768px - 1024px) */
    @media (min-width: 768px) and (max-width: 1024px) {
      .report-container {
        padding: 1.5rem;
      }

      .report-title {
        font-size: 1.8rem;
      }

      .summary-section {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
      }

      .summary-card {
        padding: 1.2rem;
      }

      .summary-number {
        font-size: 1.8rem;
      }

      .staff-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
      }

      .staff-card {
        padding: 1.2rem;
      }

      .floating-buttons {
        top: 15px;
        right: 15px;
      }

      .floating-btn {
        padding: 11px 18px;
        font-size: 0.85rem;
      }
    }

    /* Tablet Landscape (1024px - 1200px) */
    @media (min-width: 1024px) and (max-width: 1200px) {
      .report-container {
        padding: 1.8rem;
      }

      .summary-section {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      }

      .staff-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    /* Small Mobile (320px - 480px) */
    @media (max-width: 480px) {
      body {
        padding: 0.5rem;
      }

      .report-container {
        padding: 0.8rem;
      }

      .report-title {
        font-size: 1.4rem;
      }

      .report-subtitle {
        font-size: 0.9rem;
      }

      .summary-section {
        grid-template-columns: 1fr;
        gap: 0.6rem;
      }

      .summary-card {
        padding: 0.8rem;
      }

      .summary-number {
        font-size: 1.4rem;
      }

      .summary-label {
        font-size: 0.75rem;
      }

      .staff-grid {
        gap: 0.8rem;
      }

      .staff-card {
        padding: 0.8rem;
      }

      .staff-photo {
        width: 40px;
        height: 40px;
      }

      .staff-info h3 {
        font-size: 0.9rem;
      }

      .floating-buttons {
        flex-direction: column;
        gap: 5px;
      }

      .floating-btn {
        padding: 8px 12px;
        font-size: 0.75rem;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Action Buttons -->
  <div class="floating-buttons">
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
    <button class="floating-btn close-btn" onclick="closeWindow()">
      <span class="material-icons">close</span>
      Close Window
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Staff & Volunteers Report</h1>
      <p class="report-subtitle" id="report-subtitle">Loading...</p>
    </div>

    <div class="summary-section" id="summary-section">
      <div class="summary-card">
        <div class="summary-number" id="total-people">-</div>
        <div class="summary-label">Total People</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="active-staff">-</div>
        <div class="summary-label">Active Staff</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="active-volunteers">-</div>
        <div class="summary-label">Active Volunteers</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="inactive-count">-</div>
        <div class="summary-label">Inactive</div>
      </div>
    </div>

    <div id="staff-content">
      <div class="loading">Loading staff and volunteer data...</div>
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filter = urlParams.get('filter') || 'all';
    const startDate = urlParams.get('startDate');
    const endDate = urlParams.get('endDate');

    // Initialize report
    async function initializeReport() {
      if (!startDate || !endDate) {
        document.getElementById('staff-content').innerHTML = '<div class="no-data">Invalid date range provided.</div>';
        return;
      }

      // Update report subtitle
      const filterText = getFilterText(filter);
      document.getElementById('report-subtitle').textContent = `${filterText} - ${startDate} to ${endDate}`;

      // Load staff and volunteer data
      await loadStaffData();
    }

    // Get filter text for subtitle
    function getFilterText(filter) {
      switch(filter) {
        case 'current-staff': return 'Current Staff Only';
        case 'current-volunteers': return 'Current Volunteers Only';
        case 'historical': return 'Historical Data';
        default: return 'All Staff & Volunteers';
      }
    }

    // Load staff and volunteer data
    async function loadStaffData() {
      let query = supabase
        .from('staff_volunteers')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at', { ascending: false });

      // Apply filters
      switch(filter) {
        case 'current-staff':
          query = query.eq('type', 'Staff').eq('is_active', true);
          break;
        case 'current-volunteers':
          query = query.eq('type', 'Volunteer').eq('is_active', true);
          break;
        case 'historical':
          query = query.eq('is_active', false);
          break;
        // 'all' doesn't need additional filtering
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading staff data:', error);
        document.getElementById('staff-content').innerHTML = '<div class="no-data">Error loading staff and volunteer data.</div>';
        return;
      }

      if (!data || data.length === 0) {
        document.getElementById('staff-content').innerHTML = '<div class="no-data">No staff or volunteer records found for this period and filter.</div>';
        return;
      }

      // Calculate summary statistics
      calculateSummaryStats(data);

      // Generate staff grid
      generateStaffGrid(data);
    }

    // Calculate summary statistics
    function calculateSummaryStats(data) {
      const totalPeople = data.length;
      const activeStaff = data.filter(person => person.type === 'Staff' && person.is_active).length;
      const activeVolunteers = data.filter(person => person.type === 'Volunteer' && person.is_active).length;
      const inactiveCount = data.filter(person => !person.is_active).length;

      document.getElementById('total-people').textContent = totalPeople;
      document.getElementById('active-staff').textContent = activeStaff;
      document.getElementById('active-volunteers').textContent = activeVolunteers;
      document.getElementById('inactive-count').textContent = inactiveCount;
    }

    // Generate staff grid
    function generateStaffGrid(data) {
      const gridHTML = `
        <div class="staff-grid">
          ${data.map(person => `
            <div class="staff-card">
              <div class="staff-header">
                <img src="${person.photo_url || 'assets/images/placeholder-person.jpg'}" alt="${person.first_name} ${person.last_name}" class="staff-photo" />
                <div class="staff-info">
                  <h3>${person.first_name} ${person.last_name}</h3>
                  <div class="staff-type">${person.type} - ${person.position || 'No Position Listed'}</div>
                </div>
              </div>
              <div class="staff-details">
                <div class="detail-item">
                  <span class="detail-label">Email</span>
                  <span class="detail-value">${person.email || 'Not provided'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Phone</span>
                  <span class="detail-value">${person.phone || 'Not provided'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Status</span>
                  <span class="detail-value ${person.is_active ? 'status-active' : 'status-inactive'}">
                    ${person.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Start Date</span>
                  <span class="detail-value">${person.start_date ? new Date(person.start_date).toLocaleDateString() : 'Not recorded'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Emergency Contact</span>
                  <span class="detail-value">${person.emergency_contact_name || 'Not provided'}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">Emergency Phone</span>
                  <span class="detail-value">${person.emergency_contact_phone || 'Not provided'}</span>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      `;

      document.getElementById('staff-content').innerHTML = gridHTML;
    }

    // Initialize the report
    initializeReport();

    // Close window function - make it globally accessible
    function closeWindow() {
      window.location.href = 'reports.html';
    }

    // Make closeWindow globally accessible
    window.closeWindow = closeWindow;
  </script>
</body>
</html>
