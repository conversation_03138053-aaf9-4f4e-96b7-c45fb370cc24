<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>MFA Setup (Fixed)</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <h1>Multi-Factor Authentication Setup</h1>
    </header>

    <main class="main-content">
      <div class="mfa-setup-container">

        <div class="card">
          <div class="card-header">
            <h2><span class="material-icons">security</span> TOTP (Authenticator App)</h2>
          </div>
          <div class="card-content">
            <button onclick="window.setupTOTP()" class="btn-primary">Start TOTP Setup</button>
            <div id="qr-container" class="qr-code-container"></div>
            <div class="secret-key">
              <code id="secret-key"></code>
            </div>
            <div class="form-group">
              <input type="text" id="totp-code" placeholder="Enter 6-digit code" maxlength="6">
              <button onclick="window.verifyTOTP()" class="btn-secondary">Verify</button>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h2><span class="material-icons">fingerprint</span> Biometric Authentication</h2>
          </div>
          <div class="card-content">
            <p>Use Touch ID or Face ID to register a biometric login.</p>
            <button onclick="window.setupBiometric()" class="btn-primary">Register Biometric</button>
          </div>
        </div>

        <div id="status" class="feedback success" style="display: none;"></div>

      </div>
    </main>
  </div>

  <!-- External Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
  <script src="https://unpkg.com/@simplewebauthn/browser@8.3.4/dist/bundle/index.umd.min.js"></script>
  <script src="https://unpkg.com/@supabase/supabase-js@2.38.4/dist/umd/supabase.js"></script>

  <script>
    const supabase = window.supabase.createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    let enrollment = null;
    let currentChallengeId = null;

    async function setupTOTP() {
      try {
        const session = await supabase.auth.getSession();
        if (!session?.data?.session?.user) {
          throw new Error('Must be logged in with an active session.');
        }

        const { data, error } = await supabase.auth.mfa.enroll({ factorType: 'totp' });
        if (error) throw error;

        enrollment = data;
        document.getElementById('secret-key').textContent = data.totp.secret;

        const qrImage = document.createElement('img');
        qrImage.src = data.totp.qr_code;
        const qrContainer = document.getElementById('qr-container');
        qrContainer.innerHTML = '';
        qrContainer.appendChild(qrImage);

        const { data: challenge, error: challengeErr } = await supabase.auth.mfa.challenge({ factorId: data.id });
        if (challengeErr) throw challengeErr;
        currentChallengeId = challenge.id;

        showStatus('TOTP setup started. Scan the QR code and enter the 6-digit code.');
      } catch (err) {
        showStatus('TOTP Setup Error: ' + (err.message || JSON.stringify(err)), true);
      }
    }

    async function verifyTOTP() {
      const code = document.getElementById('totp-code').value;
      if (!enrollment || !currentChallengeId || !code) return showStatus('Missing code or challenge ID.', true);

      try {
        const { error } = await supabase.auth.mfa.verify({
          factorId: enrollment.id,
          challengeId: currentChallengeId,
          code
        });
        if (error) throw error;

        showStatus('TOTP verified and enabled!');
      } catch (err) {
        showStatus('TOTP Verification Failed: ' + (err.message || JSON.stringify(err)), true);
      }
    }

    function bufferToBase64url(buffer) {
      const bytes = new Uint8Array(buffer);
      let binary = '';
      for (const byte of bytes) binary += String.fromCharCode(byte);
      return btoa(binary).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    }

    // Helper function to convert string to Uint8Array
    function stringToBuffer(str) {
      const encoder = new TextEncoder();
      return encoder.encode(str);
    }

    async function setupBiometric() {
      try {
        if (!window.PublicKeyCredential) throw new Error('WebAuthn not supported');

        const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
        if (!available) throw new Error('No biometric authenticator available');

        const session = await supabase.auth.getSession();
        const user = session.data?.session?.user;
        if (!user) throw new Error('User not authenticated');

        // Generate a random challenge
        const challenge = crypto.getRandomValues(new Uint8Array(32));
        
        // Convert user.id to Uint8Array
        const userId = stringToBuffer(user.id);

        const registrationOptions = {
          publicKey: {
            rp: { 
              name: 'Cornish Birds of Prey', 
              id: (location.hostname === 'localhost' || location.hostname === '127.0.0.1') ? 'localhost' : location.hostname 
            },
            user: {
              id: userId,
              name: user.email,
              displayName: user.email || 'User'
            },
            challenge: challenge,
            pubKeyCredParams: [
              { alg: -7, type: 'public-key' },   // ES256
              { alg: -257, type: 'public-key' }  // RS256
            ],
            authenticatorSelection: { 
              authenticatorAttachment: 'platform', 
              userVerification: 'required',
              requireResidentKey: false,
              residentKey: 'discouraged'
            },
            timeout: 60000,
            attestation: 'none'
          }
        };

        // Use the native WebAuthn API directly
        const credential = await navigator.credentials.create(registrationOptions);
        
        if (!credential) throw new Error('Failed to create credential');

        // Get the credential ID in base64url format
        const credentialId = bufferToBase64url(credential.rawId);
        
        // Get the public key in base64url format
        const publicKey = bufferToBase64url(credential.response.publicKey);
        
        // Store the credential in your database
        const { error: insertError } = await supabase.from('webauthn_credentials').insert({
          user_id: user.id,
          credential_id: credentialId,
          public_key: publicKey,
          device_type: 'platform',
          device_name: navigator.userAgent.substring(0, 255), // Limit length
          is_active: true,
          created_at: new Date().toISOString()
        });

        if (insertError) throw insertError;

        showStatus('Biometric authentication successfully registered!');
        
      } catch (err) {
        console.error('Biometric setup error:', err);
        showStatus('Biometric Setup Error: ' + (err.message || 'Unknown error'), true);
      }
    }

    function showStatus(msg, isError = false) {
      const el = document.getElementById('status');
      el.className = isError ? 'feedback error' : 'feedback success';
      el.textContent = msg;
      el.style.display = 'block';
    }

    window.setupTOTP = setupTOTP;
    window.verifyTOTP = verifyTOTP;
    window.setupBiometric = setupBiometric;
  </script>
</body>
</html>