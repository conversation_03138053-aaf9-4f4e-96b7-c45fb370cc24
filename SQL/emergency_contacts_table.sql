-- Emergency Contacts Table Creation Script for Supabase
-- Run this in your Supabase SQL Editor to create the emergency_contacts table

-- Create the emergency_contacts table
CREATE TABLE IF NOT EXISTS emergency_contacts (
  id BIGSERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  role VARCHAR(255) NOT NULL,
  phone VARCHAR(50) NOT NULL,
  alt_phone VARCHAR(50),
  email VARCHAR(255),
  priority_level VARCHAR(20) NOT NULL CHECK (priority_level IN ('high', 'medium', 'low')),
  notes TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_priority ON emergency_contacts(priority_level);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_active ON emergency_contacts(is_active);
CREATE INDEX IF NOT EXISTS idx_emergency_contacts_created_by ON emergency_contacts(created_by);

-- Enable Row Level Security (RLS)
ALTER TABLE emergency_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Policy: Allow authenticated users to view active contacts
CREATE POLICY "Allow authenticated users to view active contacts" ON emergency_contacts
  FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

-- Policy: Allow authenticated users to insert contacts
CREATE POLICY "Allow authenticated users to insert contacts" ON emergency_contacts
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Allow authenticated users to update contacts (simplified for now)
CREATE POLICY "Allow authenticated users to update contacts" ON emergency_contacts
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Policy: Allow authenticated users to delete contacts (simplified for now)
CREATE POLICY "Allow authenticated users to delete contacts" ON emergency_contacts
  FOR DELETE USING (auth.role() = 'authenticated');

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_emergency_contacts_updated_at 
  BEFORE UPDATE ON emergency_contacts 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional - remove if not needed)
INSERT INTO emergency_contacts (name, role, phone, alt_phone, email, priority_level, notes, is_active) VALUES
  ('Dr. Sarah Johnson', 'Emergency Veterinarian', '01234 567890', '07123 456789', '<EMAIL>', 'high', 'Available 24/7 for emergency cases', true),
  ('Cornwall Wildlife Rescue', 'Wildlife Rescue Service', '01872 123456', NULL, '<EMAIL>', 'high', 'Specialized in bird rescue and rehabilitation', true),
  ('John Smith', 'Sanctuary Owner', '01234 987654', '07987 654321', '<EMAIL>', 'high', 'Primary contact for all sanctuary matters', true),
  ('RSPCA Cornwall', 'Animal Welfare', '0300 123 4999', NULL, '<EMAIL>', 'medium', 'General animal welfare support', true),
  ('Local Police', 'Emergency Services', '101', '999', NULL, 'medium', 'Non-emergency: 101, Emergency: 999', true),
  ('Mike Wilson', 'Maintenance Manager', '01234 555666', NULL, '<EMAIL>', 'low', 'For facility maintenance issues', true);

-- Grant necessary permissions (adjust as needed based on your setup)
-- GRANT ALL ON emergency_contacts TO authenticated;
-- GRANT USAGE, SELECT ON SEQUENCE emergency_contacts_id_seq TO authenticated;

-- Display success message
SELECT 'Emergency Contacts table created successfully!' as message;
