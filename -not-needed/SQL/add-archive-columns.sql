-- Add Archive Functionality to Animals Table
-- Migration script to add is_archived and archived_on columns

-- Add archive columns to animals table
ALTER TABLE animals 
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS archived_on TIMESTAMPTZ DEFAULT NULL;

-- Create index for better performance when filtering archived/active animals
CREATE INDEX IF NOT EXISTS idx_animals_is_archived ON animals(is_archived);

-- Add comments for documentation
COMMENT ON COLUMN animals.is_archived IS 'Indicates if the animal record is archived (true) or active (false)';
COMMENT ON COLUMN animals.archived_on IS 'Timestamp when the animal was archived, NULL if not archived';

-- Update RLS policies to include archive functionality (if needed)
-- Note: Existing RLS policies should continue to work as they don't restrict based on archive status

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'animals' 
AND column_name IN ('is_archived', 'archived_on')
ORDER BY column_name;
