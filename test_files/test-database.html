<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test</title>
</head>
<body>
    <h1>Supabase Database Test</h1>
    <div id="results"></div>
    <button onclick="testAuth()">Test Authentication</button>
    <button onclick="testRead()">Test Read Animals</button>
    <button onclick="testInsert()">Test Insert Animal</button>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
        
        window.testAuth = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing authentication...</p>';
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                
                if (session) {
                    results.innerHTML += `<p style="color: green;">✅ User authenticated: ${session.user.email}</p>`;
                    results.innerHTML += `<p>User ID: ${session.user.id}</p>`;
                    
                    // Check user profile
                    const { data: profile, error: profileError } = await supabase
                        .from('staff_volunteers')
                        .select('*')
                        .eq('user_id', session.user.id)
                        .single();
                    
                    if (profileError) {
                        results.innerHTML += `<p style="color: orange;">⚠️ Profile error: ${profileError.message}</p>`;
                    } else {
                        results.innerHTML += `<p>Profile: ${profile.name} (${profile.user_role})</p>`;
                    }
                } else {
                    results.innerHTML += '<p style="color: red;">❌ Not authenticated</p>';
                }
            } catch (error) {
                results.innerHTML += `<p style="color: red;">Exception: ${error.message}</p>`;
            }
        };
        
        window.testRead = async function() {
            const results = document.getElementById('results');
            results.innerHTML += '<p>Testing read animals...</p>';
            
            try {
                const { data, error } = await supabase
                    .from('animals')
                    .select('*')
                    .limit(5);
                
                if (error) {
                    results.innerHTML += `<p style="color: red;">Read error: ${error.message}</p>`;
                } else {
                    results.innerHTML += `<p style="color: green;">✅ Read successful: ${data.length} animals found</p>`;
                    results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                results.innerHTML += `<p style="color: red;">Exception: ${error.message}</p>`;
            }
        };
        
        window.testInsert = async function() {
            const results = document.getElementById('results');
            results.innerHTML += '<p>Testing insert animal...</p>';
            
            try {
                const testAnimal = {
                    name: 'Test Bird ' + Date.now(),
                    species: 'Test Species',
                    Group: 'Owls',
                    status: 'All Clear',
                    notes: 'Test animal created by database test'
                };
                
                // Add user attribution if authenticated
                const { data: { session } } = await supabase.auth.getSession();
                if (session) {
                    testAnimal.created_by = session.user.id;
                }
                
                results.innerHTML += `<p>Attempting to insert: ${JSON.stringify(testAnimal, null, 2)}</p>`;
                
                const { data, error } = await supabase
                    .from('animals')
                    .insert([testAnimal]);
                
                if (error) {
                    results.innerHTML += `<p style="color: red;">Insert error: ${error.message}</p>`;
                    results.innerHTML += `<p>Error details: ${JSON.stringify(error, null, 2)}</p>`;
                } else {
                    results.innerHTML += '<p style="color: green;">✅ Insert successful!</p>';
                    results.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                results.innerHTML += `<p style="color: red;">Exception: ${error.message}</p>`;
            }
        };
    </script>
</body>
</html>
