<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storage Test</title>
</head>
<body>
    <h1>Supabase Storage Test</h1>
    <div id="results"></div>
    <button onclick="testStorage()">Test Storage</button>
    <button onclick="createBucket()">Create animal-images Bucket</button>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);
        
        window.testStorage = async function() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing storage...</p>';
            
            try {
                // List all buckets
                const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
                
                if (bucketsError) {
                    results.innerHTML += `<p style="color: red;">Error listing buckets: ${bucketsError.message}</p>`;
                    return;
                }
                
                results.innerHTML += `<p>Available buckets: ${JSON.stringify(buckets, null, 2)}</p>`;
                
                // Check if animal-images bucket exists
                const animalImagesBucket = buckets.find(bucket => bucket.name === 'animal-images');

                if (animalImagesBucket) {
                    results.innerHTML += '<p style="color: green;">✅ animal-images bucket exists!</p>';

                    // Test listing files in the bucket
                    const { data: files, error: filesError } = await supabase.storage
                        .from('animal-images')
                        .list();

                    if (filesError) {
                        results.innerHTML += `<p style="color: orange;">Warning: Cannot list files in bucket: ${filesError.message}</p>`;
                    } else {
                        results.innerHTML += `<p>Files in bucket: ${files.length}</p>`;
                    }
                } else {
                    results.innerHTML += '<p style="color: red;">❌ animal-images bucket does not exist!</p>';
                }
                
            } catch (error) {
                results.innerHTML += `<p style="color: red;">Exception: ${error.message}</p>`;
            }
        };
        
        window.createBucket = async function() {
            const results = document.getElementById('results');
            results.innerHTML += '<p>Creating animal-images bucket...</p>';

            try {
                const { data, error } = await supabase.storage.createBucket('animal-images', {
                    public: true,
                    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
                    fileSizeLimit: 5242880 // 5MB
                });

                if (error) {
                    results.innerHTML += `<p style="color: red;">Error creating bucket: ${error.message}</p>`;
                } else {
                    results.innerHTML += '<p style="color: green;">✅ Bucket created successfully!</p>';
                }
            } catch (error) {
                results.innerHTML += `<p style="color: red;">Exception creating bucket: ${error.message}</p>`;
            }
        };
    </script>
</body>
</html>
