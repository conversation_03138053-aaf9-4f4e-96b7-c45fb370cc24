-- Create documents table for storing document metadata
CREATE TABLE IF NOT EXISTS documents (
  id BIGSERIAL PRIMARY KEY,
  filename TEXT NOT NULL,
  original_filename TEXT NOT NULL,
  file_path TEXT NOT NULL UNIQUE,
  file_url TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('policy', 'risk-assessment', 'emergency')),
  file_size BIGINT NOT NULL,
  mime_type TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_at ON documents(uploaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename);

-- Enable Row Level Security (RLS)
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for authenticated users
-- Note: Adjust these policies based on your security requirements
CREATE POLICY "Allow all operations for authenticated users" ON documents
  FOR ALL USING (true);

-- Create storage bucket for documents (run this in Supabase dashboard or via API)
-- This is a comment as it needs to be done via the Supabase interface:
-- 1. Go to Storage in your Supabase dashboard
-- 2. Create a new bucket called 'documents'
-- 3. Set it to public if you want direct access, or private for more security
-- 4. Configure the allowed file types: .doc, .docx, .pdf
-- 5. Set maximum file size as needed (e.g., 50MB)

-- Storage policies (adjust as needed)
-- These would be created in the Storage section of Supabase dashboard:
-- 
-- Policy name: "Allow authenticated users to upload documents"
-- Allowed operation: INSERT
-- Target roles: authenticated
-- 
-- Policy name: "Allow authenticated users to view documents" 
-- Allowed operation: SELECT
-- Target roles: authenticated
--
-- Policy name: "Allow authenticated users to delete documents"
-- Allowed operation: DELETE  
-- Target roles: authenticated

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional - remove if not needed)
-- INSERT INTO documents (filename, original_filename, file_path, file_url, category, file_size, mime_type) VALUES
-- ('sample_policy.pdf', 'Animal Care Policy.pdf', 'sample_policy.pdf', 'https://example.com/sample_policy.pdf', 'policy', 1024000, 'application/pdf'),
-- ('risk_assessment_template.docx', 'Risk Assessment Template.docx', 'risk_assessment_template.docx', 'https://example.com/risk_assessment_template.docx', 'risk-assessment', 512000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
-- ('emergency_procedures.pdf', 'Emergency Procedures.pdf', 'emergency_procedures.pdf', 'https://example.com/emergency_procedures.pdf', 'emergency', 2048000, 'application/pdf');
