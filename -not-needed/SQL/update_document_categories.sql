-- Update document categories constraint to include medical and other categories
-- This script adds 'medical' and 'other' to the allowed document categories

-- First, drop the existing check constraint
ALTER TABLE public.documents 
DROP CONSTRAINT IF EXISTS documents_category_check;

-- Add the new check constraint with all five categories
ALTER TABLE public.documents 
ADD CONSTRAINT documents_category_check 
CHECK (category = ANY (ARRAY['policy'::text, 'risk-assessment'::text, 'emergency'::text, 'medical'::text, 'other'::text]));

-- Verify the constraint was added successfully
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'public.documents'::regclass 
AND conname = 'documents_category_check';
