<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Animal Record</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />

  
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <span class="title">Animal Record</span>
      <a href="index.html" class="back-link">←</a>
    </header>

    <main class="animal-detail">
    <div class="animal-header">
      <h1 id="animal-name">Loading...</h1>
      <img id="animal-photo" src="assets/images/placeholder.jpg" alt="Animal Photo" />
      <div class="animal-info">
        <div class="info-row">
          <strong>Species:</strong>
          <span id="animal-species"></span>
        </div>
        <div class="info-row">
          <strong>Age:</strong>
          <span id="animal-age"></span>
        </div>
        <div class="info-row">
          <strong>Gender:</strong>
          <span id="animal-gender"></span>
        </div>
        <div class="info-row">
          <strong>Group:</strong>
          <span id="animal-group"></span>
        </div>
        <div class="info-row">
          <strong>Status:</strong>
          <span id="animal-status"></span>
        </div>
        <div class="info-row note-row">
          <strong>Notes:</strong>
          <span id="animal-notes"></span>
        </div>
      </div>
    </div>

    <div class="qr-container">
      <canvas id="qrcode"></canvas>
    </div>

    <hr>

    <section>
      <h3>Today's Logs</h3>
      <div id="today-logs"></div>
    </section>

    <section>
      <button id="toggle-logs">Show Recent Logs</button>
      <div id="recent-logs-section" style="display:none;">
        <h3>Recent Logs</h3>
        <div id="recent_logs"></div>
      </div>
    </section>
  </main>
  </div>

 <!-- Floating Action Button and Actions -->
<div class="fab-container">
  <div id="fab-actions" class="fab-actions">
    <button class="fab-action" onclick="openLogModal()">
      <span class="material-icons">note_add</span>
      <span class="label">Add Log</span>
    </button>
    <button class="fab-action" onclick="openEditModal()">
      <span class="material-icons">edit</span>
      <span class="label">Edit Animal</span>
    </button>
    <button class="fab-action" onclick="window.print()">
      <span class="material-icons">print</span>
      <span class="label">Print Sheet</span>
    </button>
    <button class="fab-action" onclick="openMedicalTracker()">
      <span class="material-icons">medical_services</span>
      <span class="label">Medical Tracker</span>
    </button>

  </div>
  <button class="fab-main" onclick="toggleFAB()">
    <span class="material-icons">add</span>
  </button>
</div>

  <!-- Log Modal -->
  <div id="log-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeLogModal()">✖</span>
      <h2>Add Daily Log</h2>
      <form id="log-form">
        <label class="custom-checkbox">
          <input type="checkbox" name="fed" />
          <span class="checkmark"></span> Fed
        </label>
        <label class="custom-checkbox">
          <input type="checkbox" name="cleaned" />
          <span class="checkmark"></span> Cleaned
        </label><br>
        <label>Medication Given:<br>
          <input type="text" name="medication" placeholder="e.g. Wormer, Painkiller" />
        </label><br>
        <label>Notes:<br>
          <textarea name="notes" rows="3" placeholder="e.g. Slight limp, feathers checked"></textarea>
        </label>

        <!-- User Attribution will be added here by JavaScript -->

        <button type="submit">Add Log</button>
      </form>
      <div id="log-feedback" style="margin-top:1rem;"></div>
    </div>
  </div>

  <!-- Edit Modal -->
  <div id="edit-animal-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeEditModal()">✖</span>
      <h2>Edit Animal</h2>
      <form id="edit-animal-form">
        <label>Name:<br>
          <input type="text" name="name" required />
        </label><br>
        <label>Species:<br>
          <input type="text" name="species" required />
        </label><br>
        <label>Group:<br>
          <select name="Group" required>
            <option value="">--Select Group--</option>
            <option value="Livestock">Livestock</option>
            <option value="Other (Miscellaneous)">Other (Miscellaneous)</option>
            <option value="Owls">Owls</option>
            <option value="Parrots">Parrots</option>
            <option value="Raptor">Raptor</option>
            <option value="Waterfoul">Waterfoul</option>
          </select>
        </label><br>
        <label>Status:<br>
          <select name="status" required>
            <option value="All Clear">All Clear</option>
            <option value="Under Observation">Under Observation</option>
            <option value="Unwell">Unwell</option>
            <option value="Vet Booked">Vet Booked</option>
            <option value="In Treatment">In Treatment</option>
            <option value="Recovery">Recovery</option>
            <option value="Ongoing Condition">Ongoing Condition</option>
            <option value="Palliative">Palliative</option>
            <option value="Quarantined">Quarantined</option>
            <option value="Transferred">Transferred</option>
            <option value="Deceased">Deceased</option>
          </select>
        </label><br>
        <label>Age:<br>
          <input type="text" name="Age" placeholder="e.g., Adult, Juvenile, 2 years" />
        </label><br>
        <label>Gender:<br>
          <select name="gender">
            <option value="">--Select Gender--</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
            <option value="Unknown">Unknown</option>
          </select>
        </label><br>
        <label>Photo:<br>
          <div style="margin-bottom: 0.5rem;">
            <button type="button" id="edit-upload-tab" onclick="switchEditPhotoMethod('upload')" style="background: #446c35; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px 0 0 4px; cursor: pointer;">Upload File</button>
            <button type="button" id="edit-url-tab" onclick="switchEditPhotoMethod('url')" style="background: #ccc; color: #666; border: none; padding: 0.5rem 1rem; border-radius: 0 4px 4px 0; cursor: pointer;">Enter URL</button>
          </div>

          <div id="edit-upload-method" style="display: block;">
            <input type="file" name="photo" accept="image/*" id="edit-photo-upload" />
            <small style="color: #666; font-size: 0.9rem;">Choose a new image file (JPG, PNG, etc.)</small>
          </div>

          <div id="edit-url-method" style="display: none;">
            <input type="url" name="photo_url" id="edit-photo-url" placeholder="https://example.com/image.jpg" />
            <small style="color: #666; font-size: 0.9rem;">Enter a direct link to an image</small>
          </div>
        </label><br>

        <div id="edit-photo-preview" style="display: none; margin-bottom: 1rem;">
          <img id="edit-preview-image" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #ddd;" />
          <button type="button" onclick="removeEditPhoto()" style="display: block; margin-top: 0.5rem; background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Remove New Photo</button>
        </div>

        <div id="current-photo-display" style="margin-bottom: 1rem;">
          <label style="font-weight: bold;">Current Photo:</label><br>
          <img id="current-photo-image" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #ddd; margin-top: 0.5rem;" />
        </div>
        <label>Notes:<br>
          <textarea name="notes" rows="4"></textarea>
        </label><br>

        <div style="margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;">
          <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
            <input type="checkbox" name="is_archived" id="is_archived" style="transform: scale(1.2);">
            <span style="font-weight: bold; color: #dc3545;">Archive this animal</span>
          </label>
          <small style="color: #666; margin-top: 0.5rem; display: block;">
            Archived animals will be moved to a separate section and marked as inactive. This action can be reversed.
          </small>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <button type="submit">Save Changes</button>
      </form>
    </div>
  </div>



  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script src="auth-utils.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>

  <!-- FAB Toggle Function - Global scope -->
  <script>
    let fabOpen = false;

    function toggleFAB() {
      console.log('toggleFAB called, current state:', fabOpen);
      const actions = document.getElementById('fab-actions');
      const fabMain = document.querySelector('.fab-main');

      if (!actions) {
        console.error('fab-actions element not found');
        return;
      }

      fabOpen = !fabOpen;
      actions.classList.toggle('active', fabOpen);
      fabMain.classList.toggle('active', fabOpen);

      console.log('New state:', fabOpen, 'Active class:', actions.classList.contains('active'));
    }

    function toggleMenu() {
      console.log('toggleMenu called');
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');
      console.log('Menu is currently:', isOpen ? 'open' : 'closed');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
        console.log('Menu closed');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
        console.log('Menu opened');
      }
    }

    function openEditModal() {
      // This will be populated once currentAnimal is loaded
      if (!window.currentAnimal) {
        alert('Animal data not loaded yet. Please wait a moment and try again.');
        return;
      }
      const form = document.getElementById('edit-animal-form');
      form.name.value = window.currentAnimal.name;
      form.species.value = window.currentAnimal.species;
      form.Group.value = window.currentAnimal.Group || window.currentAnimal.group || '';
      form.status.value = window.currentAnimal.status;
      form.Age.value = window.currentAnimal.Age || window.currentAnimal.age || '';
      form.gender.value = window.currentAnimal.gender || '';
      form.notes.value = window.currentAnimal.notes || '';

      // Set archive checkbox
      form.is_archived.checked = window.currentAnimal.is_archived || false;

      // Show current photo
      const currentPhotoImg = document.getElementById('current-photo-image');
      const currentPhotoDisplay = document.getElementById('current-photo-display');
      if (window.currentAnimal.photo_url) {
        currentPhotoImg.src = window.currentAnimal.photo_url;
        currentPhotoDisplay.style.display = 'block';
      } else {
        currentPhotoDisplay.style.display = 'none';
      }

      // Reset photo inputs and switch to upload method by default
      document.getElementById('edit-photo-upload').value = '';
      document.getElementById('edit-photo-url').value = '';
      document.getElementById('edit-photo-preview').style.display = 'none';
      switchEditPhotoMethod('upload');

      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('edit-animal-form');

      document.getElementById('edit-animal-modal').style.display = 'flex';
    }

    function openMedicalTracker() {
      const urlParams = new URLSearchParams(window.location.search);
      const animalId = urlParams.get('id');
      if (animalId) {
        window.location.href = `medical-tracker.html?id=${animalId}`;
      } else {
        alert('Animal ID not found. Please try again.');
      }
    }

    function openLogModal() {
      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('log-form');

      // Show the modal
      document.getElementById('log-modal').style.display = 'flex';
    }

    function closeLogModal() {
      // Reset the form
      document.getElementById('log-form').reset();

      // Clear feedback
      document.getElementById('log-feedback').textContent = '';

      // Hide the modal
      document.getElementById('log-modal').style.display = 'none';
    }

    // Switch between upload and URL methods for edit modal
    function switchEditPhotoMethod(method) {
      const uploadTab = document.getElementById('edit-upload-tab');
      const urlTab = document.getElementById('edit-url-tab');
      const uploadMethod = document.getElementById('edit-upload-method');
      const urlMethod = document.getElementById('edit-url-method');

      if (method === 'upload') {
        uploadTab.style.background = '#446c35';
        uploadTab.style.color = 'white';
        urlTab.style.background = '#ccc';
        urlTab.style.color = '#666';
        uploadMethod.style.display = 'block';
        urlMethod.style.display = 'none';
        // Clear URL input
        document.getElementById('edit-photo-url').value = '';
      } else {
        urlTab.style.background = '#446c35';
        urlTab.style.color = 'white';
        uploadTab.style.background = '#ccc';
        uploadTab.style.color = '#666';
        urlMethod.style.display = 'block';
        uploadMethod.style.display = 'none';
        // Clear file input
        document.getElementById('edit-photo-upload').value = '';
      }
      // Clear preview
      document.getElementById('edit-photo-preview').style.display = 'none';
    }

    // Photo handling functions for edit modal
    document.addEventListener('DOMContentLoaded', function() {
      const editPhotoUpload = document.getElementById('edit-photo-upload');
      const editPhotoUrl = document.getElementById('edit-photo-url');

      if (editPhotoUpload) {
        editPhotoUpload.addEventListener('change', function(e) {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
              document.getElementById('edit-preview-image').src = e.target.result;
              document.getElementById('edit-photo-preview').style.display = 'block';
            };
            reader.readAsDataURL(file);
          }
        });
      }

      if (editPhotoUrl) {
        editPhotoUrl.addEventListener('input', function(e) {
          const url = e.target.value;
          if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
            document.getElementById('edit-preview-image').src = url;
            document.getElementById('edit-photo-preview').style.display = 'block';
          } else {
            document.getElementById('edit-photo-preview').style.display = 'none';
          }
        });
      }
    });

    function removeEditPhoto() {
      document.getElementById('edit-photo-upload').value = '';
      document.getElementById('edit-photo-url').value = '';
      document.getElementById('edit-photo-preview').style.display = 'none';
    }

    // Convert file to base64
    function fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
      });
    }
  </script>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    const urlParams = new URLSearchParams(window.location.search);
    const animalId = urlParams.get('id');
    let currentAnimal = null;

    // Make currentAnimal available globally
    window.currentAnimal = null;

    async function initializeApp() {
      console.log('Starting animal page initialization...');

      // Check authentication
      console.log('Checking authentication...');
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        console.log('User not authenticated, redirecting to login');
        window.location.href = 'login.html';
        return;
      }

      console.log('✅ User authenticated:', user.email);

      if (!animalId) {
        document.body.innerHTML = '<h2>No animal ID provided.</h2>';
        return;
      }

      await loadAnimal();
      await loadLogs();
    }

    async function loadAnimal() {
      console.log('Fetching animal with ID:', animalId);

      const { data: animals, error } = await supabase
        .from('animals')
        .select('*')
        .eq('id', animalId);

      if (error) {
        console.error('Error fetching animal:', error);
        document.body.innerHTML = '<h2>Error loading animal.</h2>';
        return;
      }

      if (!animals || animals.length === 0) {
        console.log('Animal not found');
        document.body.innerHTML = '<h2>Animal not found.</h2>';
        return;
      }

      const animal = animals[0];
      console.log('Animal loaded:', animal.name);

      currentAnimal = animal;
      window.currentAnimal = animal;

      document.getElementById('animal-name').textContent = animal.name;
      document.getElementById('animal-species').textContent = animal.species;
      document.getElementById('animal-age').textContent = animal.Age || animal.age || 'Unknown';
      document.getElementById('animal-gender').textContent = animal.gender || 'Unknown';
      document.getElementById('animal-group').textContent = animal.Group || animal.group || 'Unknown';
      document.getElementById('animal-status').textContent = animal.status;
      document.getElementById('animal-notes').textContent = animal.notes || '—';
      document.getElementById('animal-photo').src = animal.photo_url || 'assets/images/placeholder.jpg';

      const qrLink = `${window.location.origin}${window.location.pathname}?id=${animal.id}`;
      QRCode.toCanvas(document.getElementById('qrcode'), qrLink, { width: 100 });
    }

    function formatLog(log) {
      const dateStr = new Date(log.created_on || '').toLocaleDateString('en-UK');
      return `
        <div class="log-entry">
          <table class="log-table">
            <tr><th>Date:</th><td>${dateStr}</td></tr>
            <tr><th>Fed:</th><td>${log.fed ? 'Yes' : 'No'}</td></tr>
            <tr><th>Cleaned:</th><td>${log.cleaned ? 'Yes' : 'No'}</td></tr>
            <tr><th>Medication:</th><td>${log.medication || 'N/A'}</td></tr>
            <tr><th>Notes:</th><td>${log.notes || '—'}</td></tr>
          </table>
        </div>`;
    }

    async function loadLogs() {
      console.log('Fetching logs for animal:', animalId);

      const { data: logs, error } = await supabase
        .from('daily_logs')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_on', { ascending: false });

      if (error) {
        console.error('Error fetching logs:', error);
        document.getElementById('today-logs').innerHTML = '<p>Error loading logs.</p>';
        document.getElementById('recent_logs').innerHTML = '<p>Error loading logs.</p>';
        return;
      }

      console.log('Logs loaded:', logs.length);

      const today = new Date().toISOString().split('T')[0];
      const todayLogs = logs.filter(log => log.created_on?.split('T')[0] === today);
      const pastLogs = logs.filter(log => log.created_on?.split('T')[0] !== today);

      document.getElementById('today-logs').innerHTML = todayLogs.length
        ? todayLogs.map(formatLog).join('')
        : '<p>No logs today yet.</p>';

      document.getElementById('recent_logs').innerHTML = pastLogs.map(formatLog).join('');
    }

    document.getElementById('edit-animal-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      const data = Object.fromEntries(formData.entries());

      // Handle enum fields - convert empty strings to null for database enums
      if (data.gender === '' || data.gender === undefined) {
        data.gender = null;
      }

      // Clean up any other empty string values that might be enum fields
      Object.keys(data).forEach(key => {
        if (data[key] === '') {
          // For required fields (Group, Status), keep empty string to trigger validation
          // For optional enum fields, convert to null
          if (key === 'gender') {
            data[key] = null;
          }
        }
      });

      // Handle photo - either file upload or URL
      const photoFile = formData.get('photo');
      const photoUrl = formData.get('photo_url');

      if (photoFile && photoFile.size > 0) {
        // File upload method
        try {
          const base64 = await fileToBase64(photoFile);
          data.photo_url = base64;
        } catch (error) {
          console.error('Error processing photo:', error);
          alert('Error processing photo. Please try again.');
          return;
        }
      } else if (photoUrl && photoUrl.trim()) {
        // URL method
        data.photo_url = photoUrl.trim();
      } else {
        // No new photo provided - preserve existing photo URL
        data.photo_url = window.currentAnimal.photo_url;
      }

      // Remove the photo file from data since we're using photo_url
      delete data.photo;

      // Handle archive functionality
      const isArchived = data.is_archived === 'on'; // Checkbox value
      data.is_archived = isArchived;

      if (isArchived && !window.currentAnimal.is_archived) {
        // Animal is being archived - set archived_on timestamp
        data.archived_on = new Date().toISOString();
      } else if (!isArchived && window.currentAnimal.is_archived) {
        // Animal is being unarchived - clear archived_on timestamp
        data.archived_on = null;
      } else {
        // No change in archive status - preserve existing archived_on
        data.archived_on = window.currentAnimal.archived_on;
      }

      // Add user attribution for update
      const dataWithAttribution = window.authUtils.addUserAttribution(data, true);

      console.log('Updating animal with data:', dataWithAttribution);

      const { error } = await supabase
        .from('animals')
        .update(dataWithAttribution)
        .eq('id', animalId);

      if (error) {
        alert('Update failed');
        console.error('Error updating animal:', error);
      } else {
        alert('Animal updated successfully!');
        closeEditModal();
        location.reload();
      }
    });

    document.getElementById('log-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);

      // Get current user info for logged_by field
      const userInfo = window.authUtils.getCurrentUserInfo();

      const logData = {
        animal_id: animalId,
        fed: formData.has('fed') ? 'Yes' : 'No',
        cleaned: formData.has('cleaned') ? 'Yes' : 'No',
        medication: formData.get('medication'),
        notes: formData.get('notes'),
        logged_by: userInfo ? userInfo.name : 'Unknown Staff'
      };

      // Add user attribution
      const logDataWithAttribution = window.authUtils.addUserAttribution(logData, false);

      const { data, error } = await supabase
        .from('daily_logs')
        .insert([logDataWithAttribution]);

      if (error) {
        document.getElementById('log-feedback').textContent = '❌ Error saving log.';
        console.error('Error saving log:', error);
      } else {
        document.getElementById('log-feedback').textContent = '✅ Log saved.';
        setTimeout(() => {
          closeLogModal();
          loadLogs();
        }, 1500); // Show success message for 1.5 seconds before closing
      }
    });

    document.getElementById('toggle-logs').addEventListener('click', () => {
      const section = document.getElementById('recent-logs-section');
      section.style.display = section.style.display === 'none' ? 'block' : 'none';
      document.getElementById('toggle-logs').textContent = section.style.display === 'none' ? 'Show Recent Logs' : 'Hide Recent Logs';
    });

    // Function to open medical tracker for current animal
    function openMedicalTracker() {
      const urlParams = new URLSearchParams(window.location.search);
      const animalId = urlParams.get('id');

      if (animalId) {
        window.location.href = `medical-tracker.html?id=${animalId}`;
      } else {
        alert('No animal selected. Please select an animal from the Dashboard first.');
      }
    }

    function closeEditModal() {
      document.getElementById('edit-animal-modal').style.display = 'none';
    }














    // Initialize the app
    initializeApp();
    window.closeEditModal = closeEditModal;
  </script>
</body>
</html>