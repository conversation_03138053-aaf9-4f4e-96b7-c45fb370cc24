-- Add user_id column to documents table if it doesn't exist
ALTER TABLE documents ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id);

-- Drop the existing policy
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON documents;

-- Create a more specific policy that uses auth.uid()
CREATE POLICY "Allow authenticated users to manage documents" ON documents
  FOR ALL USING (auth.uid() IS NOT NULL);

-- Alternative: If you want users to only see their own documents, use this instead:
-- CREATE POLICY "Users can manage their own documents" ON documents
--   FOR ALL USING (auth.uid() = user_id);

-- Create index on user_id for better performance
CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
