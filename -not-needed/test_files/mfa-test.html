<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>MFA Test</title>
</head>
<body>
  <h1>MFA Test Page</h1>
  
  <button onclick="testSetupTOTP()">Test Setup TOTP</button>
  <button onclick="testSetupBiometric()">Test Setup Biometric</button>
  
  <div id="output"></div>

  <!-- Supabase -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
  
  <script>
    // Test functions
    function testSetupTOTP() {
      document.getElementById('output').innerHTML = 'setupTOTP function called!';
      console.log('setupTOTP function called');
    }
    
    function testSetupBiometric() {
      document.getElementById('output').innerHTML = 'setupBiometric function called!';
      console.log('setupBiometric function called');
    }
    
    // Test Supabase
    console.log('Supabase available:', typeof window.supabase);
    
    if (window.supabase) {
      const supabase = window.supabase.createClient(
        'https://wkclogfpyykwgjhhshsi.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
      );
      console.log('Supabase client created:', supabase);
    }
  </script>
</body>
</html>
