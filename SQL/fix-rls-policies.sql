-- Fix RLS Policies for Cornish Birds of Prey Center
-- This script fixes the overly restrictive RLS policies that are preventing data access

-- First, let's drop the problematic policies and recreate them properly

-- Animals table - Fix policies
DROP POLICY IF EXISTS "Admins can do everything with animals" ON animals;
DROP POLICY IF EXISTS "Staff can create, read, update animals" ON animals;
DROP POLICY IF EXISTS "Volunteers can read animals and add logs" ON animals;
DROP POLICY IF EXISTS "Allow update for all users" ON animals;
DROP POLICY IF EXISTS "Enable read access for all users" ON animals;

-- Create new, properly working policies for animals
CREATE POLICY "Allow authenticated users to read animals" ON animals
  FOR SELECT USING (
    auth.role() = 'authenticated'
  );

CREATE POLICY "Allow authenticated users to insert animals" ON animals
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

CREATE POLICY "Allow authenticated users to update animals" ON animals
  FOR UPDATE USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

CREATE POLICY "Allow only admins to delete animals" ON animals
  FOR DELETE USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role = 'Admin' 
      AND is_active = true
    )
  );

-- Medical episodes - Fix policies
DROP POLICY IF EXISTS "Admins and Staff can manage medical episodes" ON medical_episodes;
DROP POLICY IF EXISTS "Volunteers can view open medical episodes" ON medical_episodes;
DROP POLICY IF EXISTS "Allow all operations on medical_episodes" ON medical_episodes;

CREATE POLICY "Allow authenticated users to read medical episodes" ON medical_episodes
  FOR SELECT USING (
    auth.role() = 'authenticated' AND (
      -- Admins and Staff can see all episodes
      EXISTS (
        SELECT 1 FROM staff_volunteers 
        WHERE user_id = auth.uid() 
        AND user_role IN ('Admin', 'Staff') 
        AND is_active = true
      ) OR
      -- Volunteers can only see open episodes
      (
        status != 'resolved' AND
        EXISTS (
          SELECT 1 FROM staff_volunteers 
          WHERE user_id = auth.uid() 
          AND user_role = 'Volunteer' 
          AND is_active = true
        )
      )
    )
  );

CREATE POLICY "Allow staff to manage medical episodes" ON medical_episodes
  FOR ALL USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

-- Medical interventions - Fix policies
DROP POLICY IF EXISTS "Allow all operations on medical_interventions" ON medical_interventions;

CREATE POLICY "Allow authenticated users to read medical interventions" ON medical_interventions
  FOR SELECT USING (
    auth.role() = 'authenticated'
  );

CREATE POLICY "Allow staff to manage medical interventions" ON medical_interventions
  FOR ALL USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

-- Daily logs - Fix policies
DROP POLICY IF EXISTS "Allow select for all users" ON daily_logs;

CREATE POLICY "Allow authenticated users to read daily logs" ON daily_logs
  FOR SELECT USING (
    auth.role() = 'authenticated'
  );

CREATE POLICY "Allow authenticated users to add daily logs" ON daily_logs
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND is_active = true
    )
  );

CREATE POLICY "Allow staff to manage daily logs" ON daily_logs
  FOR UPDATE USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

CREATE POLICY "Allow only admins to delete daily logs" ON daily_logs
  FOR DELETE USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role = 'Admin' 
      AND is_active = true
    )
  );

-- Documents - Fix policies
DROP POLICY IF EXISTS "Allow authenticated users to manage documents" ON documents;

CREATE POLICY "Allow authenticated users to read documents" ON documents
  FOR SELECT USING (
    auth.role() = 'authenticated'
  );

CREATE POLICY "Allow staff to manage documents" ON documents
  FOR ALL USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

-- Staff volunteers - Fix policies
DROP POLICY IF EXISTS "Admins can manage all staff and volunteers" ON staff_volunteers;
DROP POLICY IF EXISTS "Staff can view other staff and volunteers" ON staff_volunteers;
DROP POLICY IF EXISTS "Users can view their own profile" ON staff_volunteers;

CREATE POLICY "Allow users to view their own profile" ON staff_volunteers
  FOR SELECT USING (
    auth.role() = 'authenticated' AND user_id = auth.uid()
  );

CREATE POLICY "Allow staff to view all profiles" ON staff_volunteers
  FOR SELECT USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role IN ('Admin', 'Staff') 
      AND is_active = true
    )
  );

CREATE POLICY "Allow admins to manage all staff and volunteers" ON staff_volunteers
  FOR ALL USING (
    auth.role() = 'authenticated' AND
    EXISTS (
      SELECT 1 FROM staff_volunteers 
      WHERE user_id = auth.uid() 
      AND user_role = 'Admin' 
      AND is_active = true
    )
  );

-- Fix the function security issues mentioned in the warnings
-- Update functions to have proper search_path

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION create_user_preferences()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF NEW.user_id IS NOT NULL AND OLD.user_id IS NULL THEN
    INSERT INTO user_preferences (user_id) VALUES (NEW.user_id);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION log_user_action()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_log (user_id, action, table_name, record_id, new_values)
    VALUES (auth.uid(), 'CREATE', TG_TABLE_NAME, NEW.id, to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_log (user_id, action, table_name, record_id, old_values, new_values)
    VALUES (auth.uid(), 'UPDATE', TG_TABLE_NAME, NEW.id, to_jsonb(OLD), to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_log (user_id, action, table_name, record_id, old_values)
    VALUES (auth.uid(), 'DELETE', TG_TABLE_NAME, OLD.id, to_jsonb(OLD));
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get user role for easier policy checking
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN (
    SELECT user_role
    FROM staff_volunteers
    WHERE user_id = auth.uid()
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;

-- Add some helpful comments
COMMENT ON FUNCTION get_user_role() IS 'Returns the user role for the currently authenticated user';
COMMENT ON POLICY "Allow authenticated users to read animals" ON animals IS 'All authenticated users can view animals';
COMMENT ON POLICY "Allow staff to manage documents" ON documents IS 'Only Admin and Staff can create/update/delete documents';

-- Note: After running this, you should see animals in the dashboard
-- The policies now properly allow authenticated users to read data while maintaining security
