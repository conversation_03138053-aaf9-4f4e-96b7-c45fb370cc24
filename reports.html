<!DOCTYPE html>
<html lang="en">
<head>
  <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Reports – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Generate Report</h1>
      <div class="header-actions">
        <a href="dashboard.html" class="back-link" title="Back to Dashboard">
          <span class="material-icons">arrow_back</span>
        </a>
        <button class="logout-btn" onclick="logout()" title="Logout">
          <span class="material-icons">logout</span>
        </button>
      </div>
    </header>

    <main class="dashboard">
      <section class="report-type-buttons">
        <button class="report-type-btn active" data-type="animal">
          <span class="material-icons">pets</span>
          Animal Report
        </button>
        <button class="report-type-btn" data-type="daily-logs">
          <span class="material-icons">assignment</span>
          Daily Logs Report
        </button>
        <button class="report-type-btn" data-type="staff-volunteers">
          <span class="material-icons">people</span>
          Staff & Volunteers Report
        </button>
        <button class="report-type-btn" data-type="adoptions-donations">
          <span class="material-icons">volunteer_activism</span>
          Adoptions & Donations Report
        </button>
        <button class="report-type-btn" data-type="document-compliance">
          <span class="material-icons">description</span>
          Document Compliance Report
        </button>
        <button class="report-type-btn" data-type="animal-deaths">
          <span class="material-icons">pets</span>
          Animal Deaths Report
        </button>
      </section>

      <!-- Animal Report Builder -->
      <section class="report-builder animal-report-builder" id="animal-report-builder">
        <div class="search-section">
          <label for="animal-search">Select Animal:</label>
          <input type="text" id="animal-search" class="search-input" placeholder="Search by name..." />
          <div id="animal-results" class="animal-results"></div>
        </div>

        <div class="report-options">
          <h3>Include in Report:</h3>
          <div class="column-buttons-group">
            <button type="button" class="column-btn active" data-section="logs">
              <span class="material-icons">assignment</span>
              Daily Logs
            </button>
            <button type="button" class="column-btn" data-section="medical">
              <span class="material-icons">medical_services</span>
              Medical Episodes
            </button>
            <button type="button" class="column-btn" data-section="adoptions">
              <span class="material-icons">volunteer_activism</span>
              Adoptions & Donations
            </button>
            <button type="button" class="column-btn" id="full-history-btn">
              <span class="material-icons">history</span>
              Full History
            </button>
          </div>
        </div>

        <div class="date-range-section" id="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="start-date">Start Date:</label>
              <input type="date" id="start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="end-date">End Date:</label>
              <input type="date" id="end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Daily Logs Report Builder -->
      <section class="report-builder daily-logs-report-builder" id="daily-logs-report-builder" style="display: none;">
        <div class="report-options">
          <h3>Report Options:</h3>
          <div class="column-buttons-group">
            <button type="button" class="column-btn" id="today-tracker-btn">
              <span class="material-icons">today</span>
              <strong>Today Tracker</strong> (Override all other options)
            </button>
            <button type="button" class="column-btn active" data-logs-option="feeding">
              <span class="material-icons">restaurant</span>
              Feeding Status
            </button>
            <button type="button" class="column-btn active" data-logs-option="cleaning">
              <span class="material-icons">cleaning_services</span>
              Cleaning Status
            </button>
            <button type="button" class="column-btn active" data-logs-option="staff-attribution">
              <span class="material-icons">person</span>
              Staff Attribution
            </button>
            <button type="button" class="column-btn active" data-logs-option="notes">
              <span class="material-icons">note</span>
              Notes
            </button>
          </div>
        </div>

        <div class="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="logs-start-date">Start Date:</label>
              <input type="date" id="logs-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="logs-end-date">End Date:</label>
              <input type="date" id="logs-end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Staff & Volunteers Report Builder -->
      <section class="report-builder staff-volunteers-report-builder" id="staff-volunteers-report-builder" style="display: none;">
        <div class="filter-section">
          <h3>Filter Options:</h3>
          <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="current-staff">Current Staff</button>
            <button class="filter-btn" data-filter="current-volunteers">Current Volunteers</button>
            <button class="filter-btn" data-filter="historical">Historical Data</button>
          </div>
        </div>

        <div class="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="staff-start-date">Start Date:</label>
              <input type="date" id="staff-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="staff-end-date">End Date:</label>
              <input type="date" id="staff-end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Adoptions & Donations Report Builder -->
      <section class="report-builder adoptions-donations-report-builder" id="adoptions-donations-report-builder" style="display: none;">
        <div class="filter-section">
          <h3>Filter Options:</h3>
          <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All Records</button>
            <button class="filter-btn" data-filter="expiring-1">Expiring in 1 Month</button>
            <button class="filter-btn" data-filter="expiring-3">Expiring in 3 Months</button>
            <button class="filter-btn" data-filter="expiring-6">Expiring in 6 Months</button>
          </div>
        </div>

        <div class="report-columns-section">
          <h3>Report Columns:</h3>
          <div class="columns-info">
            <p><strong>Default columns:</strong> Animal Name, Type, Amount, Donor Name, Start Date, End Date</p>
          </div>
          <div class="column-buttons-group">
            <button type="button" class="column-btn" data-column="donor_email">
              <span class="material-icons">email</span>
              Donor Email
            </button>
            <button type="button" class="column-btn" data-column="status">
              <span class="material-icons">info</span>
              Status
            </button>
            <button type="button" class="column-btn" data-column="notes">
              <span class="material-icons">note</span>
              Notes
            </button>
            <button type="button" class="column-btn" data-column="created_at">
              <span class="material-icons">schedule</span>
              Date Created
            </button>
            <button type="button" class="column-btn" data-column="created_by">
              <span class="material-icons">person</span>
              Created By
            </button>
            <button type="button" class="column-btn" data-column="updated_by">
              <span class="material-icons">edit</span>
              Last Updated By
            </button>
          </div>
        </div>

        <div class="animal-filter-section">
          <label for="adoption-animal-search">Filter by Animal (Optional):</label>
          <input type="text" id="adoption-animal-search" class="search-input" placeholder="Search by animal name..." />
          <div id="adoption-animal-results" class="animal-results"></div>
        </div>

        <div class="date-range-section">
          <h3>Date Range:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="adoption-start-date">Start Date:</label>
              <input type="date" id="adoption-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="adoption-end-date">End Date:</label>
              <input type="date" id="adoption-end-date" class="date-input" />
            </div>
          </div>
        </div>
      </section>

      <!-- Document Compliance Report Builder -->
      <section class="report-builder document-compliance-report-builder" id="document-compliance-report-builder" style="display: none;">
        <div class="filter-section">
          <h3>Filter Options:</h3>
          <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All Documents</button>
            <button class="filter-btn" data-filter="expiring-1">Expiring in 1 Month</button>
            <button class="filter-btn" data-filter="expiring-3">Expiring in 3 Months</button>
            <button class="filter-btn" data-filter="overdue">Overdue for Review</button>
          </div>
        </div>

        <div class="report-options">
          <h3>Include in Report:</h3>
          <div class="column-buttons-group">
            <button type="button" class="column-btn active" data-column="original_filename">
              <span class="material-icons">description</span>
              Document Name
            </button>
            <button type="button" class="column-btn active" data-column="category">
              <span class="material-icons">category</span>
              Document Type
            </button>
            <button type="button" class="column-btn active" data-column="uploaded_at">
              <span class="material-icons">upload</span>
              Creation Date
            </button>
            <button type="button" class="column-btn active" data-column="review_date">
              <span class="material-icons">schedule</span>
              Review Date
            </button>
            <button type="button" class="column-btn" data-column="file_size">
              <span class="material-icons">storage</span>
              File Size
            </button>
            <button type="button" class="column-btn" data-column="created_at">
              <span class="material-icons">event</span>
              Document Created Date
            </button>
          </div>
        </div>
      </section>

      <!-- Animal Deaths Report Builder -->
      <section class="report-builder animal-deaths-report-builder" id="animal-deaths-report-builder" style="display: none;">
        <div class="date-range-section">
          <h3>Date Range Filter:</h3>
          <div class="date-inputs">
            <div class="date-group">
              <label for="deaths-start-date">Start Date:</label>
              <input type="date" id="deaths-start-date" class="date-input" />
            </div>
            <div class="date-group">
              <label for="deaths-end-date">End Date:</label>
              <input type="date" id="deaths-end-date" class="date-input" />
            </div>
          </div>
          <p class="date-help-text">Filter animals by date of death within this range (inclusive)</p>
        </div>

        <div class="report-options">
          <h3>Columns Included:</h3>
          <div class="column-buttons-group">
            <button type="button" class="column-btn active" data-column="name">
              <span class="material-icons">pets</span>
              Name
            </button>
            <button type="button" class="column-btn active" data-column="species">
              <span class="material-icons">category</span>
              Species
            </button>
            <button type="button" class="column-btn active" data-column="gender">
              <span class="material-icons">wc</span>
              Gender
            </button>
            <button type="button" class="column-btn active" data-column="age">
              <span class="material-icons">schedule</span>
              Age
            </button>
            <button type="button" class="column-btn active" data-column="date_of_death">
              <span class="material-icons">event</span>
              Date of Death
            </button>
            <button type="button" class="column-btn active" data-column="reason_for_death">
              <span class="material-icons">info</span>
              Reason for Death
            </button>
          </div>
        </div>
      </section>

      <section class="report-action">
        <button class="btn-primary" id="generate-report-btn">
          <span class="material-icons">description</span>
          Generate Report
        </button>
      </section>
    </main>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li class="active"><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
    </ul>
  </nav>

  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <script src="auth-utils.js"></script>
  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    const searchInput = document.getElementById('animal-search');
    const resultsContainer = document.getElementById('animal-results');
    let selectedAnimal = null;
    let currentReportType = 'animal';

    window.toggleMenu = function() {
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
      }
    };

    // Report type switching functionality
    function switchReportType(type) {
      currentReportType = type;

      // Update button states
      document.querySelectorAll('.report-type-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-type="${type}"]`).classList.add('active');

      // Hide all report builders
      document.querySelectorAll('.report-builder').forEach(builder => {
        builder.style.display = 'none';
      });

      // Show selected report builder
      const targetBuilder = document.getElementById(`${type}-report-builder`);
      if (targetBuilder) {
        targetBuilder.style.display = 'block';
      }

      // Set default dates for the current report type
      setDefaultDates(type);
    }

    // Set default dates based on report type
    function setDefaultDates(type) {
      const today = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(today.getDate() - 30);

      const todayStr = today.toISOString().split('T')[0];
      const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

      switch(type) {
        case 'animal':
          document.getElementById('start-date').value = thirtyDaysAgoStr;
          document.getElementById('end-date').value = todayStr;
          break;
        case 'daily-logs':
          document.getElementById('logs-start-date').value = thirtyDaysAgoStr;
          document.getElementById('logs-end-date').value = todayStr;
          break;
        case 'staff-volunteers':
          document.getElementById('staff-start-date').value = thirtyDaysAgoStr;
          document.getElementById('staff-end-date').value = todayStr;
          break;
        case 'adoptions-donations':
          document.getElementById('adoption-start-date').value = thirtyDaysAgoStr;
          document.getElementById('adoption-end-date').value = todayStr;
          break;
        case 'animal-deaths':
          document.getElementById('deaths-start-date').value = thirtyDaysAgoStr;
          document.getElementById('deaths-end-date').value = todayStr;
          break;
      }
    }

    // Initialize report type buttons
    document.querySelectorAll('.report-type-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const type = btn.getAttribute('data-type');
        switchReportType(type);
      });
    });

    // Handle full history button
    document.getElementById('full-history-btn').addEventListener('click', function() {
      this.classList.toggle('active');
      const dateRangeSection = document.getElementById('date-range-section');
      if (this.classList.contains('active')) {
        dateRangeSection.style.display = 'none';
      } else {
        dateRangeSection.style.display = 'block';
      }
    });

    // Initialize filter buttons for different report types
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        // Remove active class from siblings
        this.parentElement.querySelectorAll('.filter-btn').forEach(sibling => {
          sibling.classList.remove('active');
        });
        // Add active class to clicked button
        this.classList.add('active');

        // Auto-generate report for adoptions-donations filter buttons
        if (this.closest('.adoptions-donations-report-builder')) {
          const filterType = this.getAttribute('data-filter');
          if (filterType && filterType !== 'all') {
            generateExpiringReport(filterType);
          }
        }
      });
    });

    // Initialize column button functionality
    document.querySelectorAll('.column-btn').forEach(btn => {
      // Skip buttons that have special handlers (full-history-btn and today-tracker-btn)
      if (btn.id === 'full-history-btn' || btn.id === 'today-tracker-btn') {
        return;
      }

      btn.addEventListener('click', function() {
        this.classList.toggle('active');
      });
    });

    // Initialize default report type and dates
    switchReportType('animal');

    // Handle Today Tracker button behavior
    document.getElementById('today-tracker-btn').addEventListener('click', function() {
      this.classList.toggle('active');
      const dateRangeSection = document.querySelector('.daily-logs-report-builder .date-range-section');
      const otherOptions = document.querySelectorAll('.column-btn[data-logs-option]');

      if (this.classList.contains('active')) {
        // Disable date range and other options
        if (dateRangeSection) dateRangeSection.style.opacity = '0.5';
        otherOptions.forEach(option => {
          option.disabled = true;
          option.style.opacity = '0.5';
          option.style.pointerEvents = 'none';
        });
      } else {
        // Re-enable date range and other options
        if (dateRangeSection) dateRangeSection.style.opacity = '1';
        otherOptions.forEach(option => {
          option.disabled = false;
          option.style.opacity = '1';
          option.style.pointerEvents = 'auto';
        });
      }
    });

    searchInput.addEventListener('input', async () => {
      const searchTerm = searchInput.value.trim().toLowerCase();
      if (searchTerm.length < 2) {
        resultsContainer.innerHTML = '';
        return;
      }

      const { data, error } = await supabase
        .from('animals')
        .select('id, name, species, Group, photo_url, status, Age, notes')
        .ilike('name', `%${searchTerm}%`)
        .limit(10);

      if (error) {
        resultsContainer.innerHTML = '<p>Error loading animals.</p>';
        console.error(error);
        return;
      }

      if (!data.length) {
        resultsContainer.innerHTML = '<p>No animals found.</p>';
        return;
      }

      resultsContainer.innerHTML = data.map(animal => `
        <div class="report-animal-card" data-id="${animal.id}">
          <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name}" />
          <div>
            <strong>${animal.name}</strong><br>
            <small>${animal.species || 'Unknown Species'} - ${animal.Group || 'No Group'}</small>
          </div>
        </div>
      `).join('');

      document.querySelectorAll('.report-animal-card').forEach(card => {
        card.addEventListener('click', () => {
          // Remove selected class from all cards
          document.querySelectorAll('.report-animal-card').forEach(c => c.classList.remove('selected'));
          // Add selected class to clicked card
          card.classList.add('selected');
          const id = card.getAttribute('data-id');
          selectedAnimal = data.find(a => String(a.id) === String(id));
          console.log('Selected:', selectedAnimal);
        });
      });
    });

    // Handle adoptions & donations animal search
    const adoptionAnimalSearch = document.getElementById('adoption-animal-search');
    const adoptionAnimalResults = document.getElementById('adoption-animal-results');
    let selectedAdoptionAnimal = null;

    adoptionAnimalSearch.addEventListener('input', async () => {
      const searchTerm = adoptionAnimalSearch.value.trim().toLowerCase();
      if (searchTerm.length < 2) {
        adoptionAnimalResults.innerHTML = '';
        selectedAdoptionAnimal = null;
        return;
      }

      const { data, error } = await supabase
        .from('animals')
        .select('id, name, species, Group, photo_url')
        .ilike('name', `%${searchTerm}%`)
        .limit(10);

      if (error) {
        adoptionAnimalResults.innerHTML = '<p>Error loading animals.</p>';
        console.error(error);
        return;
      }

      if (!data.length) {
        adoptionAnimalResults.innerHTML = '<p>No animals found.</p>';
        return;
      }

      adoptionAnimalResults.innerHTML = data.map(animal => `
        <div class="report-animal-card" data-id="${animal.id}" data-name="${animal.name}">
          <img src="${animal.photo_url || 'assets/images/placeholder.jpg'}" alt="${animal.name}" />
          <div>
            <strong>${animal.name}</strong><br>
            <small>${animal.species || 'Unknown Species'} - ${animal.Group || 'No Group'}</small>
          </div>
        </div>
      `).join('');

      document.querySelectorAll('#adoption-animal-results .report-animal-card').forEach(card => {
        card.addEventListener('click', () => {
          // Remove selected class from all cards
          document.querySelectorAll('#adoption-animal-results .report-animal-card').forEach(c => c.classList.remove('selected'));
          // Add selected class to clicked card
          card.classList.add('selected');
          const id = card.getAttribute('data-id');
          const name = card.getAttribute('data-name');
          selectedAdoptionAnimal = data.find(a => String(a.id) === String(id));
          // Update the search input with selected animal name
          adoptionAnimalSearch.value = name;
          // Hide results
          adoptionAnimalResults.innerHTML = '';
          console.log('Selected adoption animal:', selectedAdoptionAnimal);
        });
      });
    });

    document.getElementById('generate-report-btn').addEventListener('click', () => {
      switch(currentReportType) {
        case 'animal':
          generateAnimalReport();
          break;
        case 'daily-logs':
          generateDailyLogsReport();
          break;
        case 'staff-volunteers':
          generateStaffVolunteersReport();
          break;
        case 'adoptions-donations':
          generateAdoptionsDonationsReport();
          break;
        case 'document-compliance':
          generateDocumentComplianceReport();
          break;
        case 'animal-deaths':
          generateAnimalDeathsReport();
          break;
        default:
          alert('Please select a report type.');
      }
    });

    // Generate Animal Report
    function generateAnimalReport() {
      if (!selectedAnimal) {
        alert('Please select an animal to generate the report.');
        return;
      }

      const selectedSections = Array.from(document.querySelectorAll('.column-btn.active[data-section]')).map(btn => btn.getAttribute('data-section'));
      const fullHistory = document.getElementById('full-history-btn').classList.contains('active');
      const startDate = document.getElementById('start-date').value;
      const endDate = document.getElementById('end-date').value;

      // Build URL parameters
      const params = new URLSearchParams({
        animalId: selectedAnimal.id,
        animalName: selectedAnimal.name,
        sections: selectedSections.join(','),
        fullHistory: fullHistory.toString(),
        startDate: fullHistory ? '' : startDate,
        endDate: fullHistory ? '' : endDate
      });

      // Open animal report in new window
      window.open(`animal-report.html?${params.toString()}`, '_blank');
    }

    // Generate Daily Logs Report
    function generateDailyLogsReport() {
      const todayTrackerChecked = document.getElementById('today-tracker-btn').classList.contains('active');

      if (todayTrackerChecked) {
        // Generate Today Tracker report
        window.open('today-report.html?type=today', '_blank');
        return;
      }

      const selectedOptions = Array.from(document.querySelectorAll('.column-btn.active[data-logs-option]')).map(btn => btn.getAttribute('data-logs-option'));
      const startDate = document.getElementById('logs-start-date').value;
      const endDate = document.getElementById('logs-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      const params = new URLSearchParams({
        options: selectedOptions.join(','),
        startDate: startDate,
        endDate: endDate
      });

      // Open daily logs report in new window
      window.open(`daily-logs-report.html?${params.toString()}`, '_blank');
    }

    // Generate Staff & Volunteers Report
    function generateStaffVolunteersReport() {
      const activeFilter = document.querySelector('.staff-volunteers-report-builder .filter-btn.active');
      const filterType = activeFilter ? activeFilter.getAttribute('data-filter') : 'all';
      const startDate = document.getElementById('staff-start-date').value;
      const endDate = document.getElementById('staff-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      const params = new URLSearchParams({
        filter: filterType,
        startDate: startDate,
        endDate: endDate
      });

      // Open staff volunteers report in new window
      window.open(`staff-volunteers-report.html?${params.toString()}`, '_blank');
    }

    // Generate Expiring Report (Auto-triggered by filter buttons)
    function generateExpiringReport(filterType) {
      // Set date range to cover all records for expiring filters
      const today = new Date();
      const startDate = '2020-01-01'; // Far back start date to include all records
      const endDate = new Date(today.getFullYear() + 10, 11, 31).toISOString().split('T')[0]; // Far future end date

      const params = new URLSearchParams({
        filter: filterType,
        startDate: startDate,
        endDate: endDate,
        columns: '', // Use default columns only
        animalFilter: ''
      });

      // Open adoptions donations report in new window
      window.open(`adoptions-donations-report.html?${params.toString()}`, '_blank');
    }

    // Generate Adoptions & Donations Report
    function generateAdoptionsDonationsReport() {
      const activeFilter = document.querySelector('.adoptions-donations-report-builder .filter-btn.active');
      const filterType = activeFilter ? activeFilter.getAttribute('data-filter') : 'all';
      const startDate = document.getElementById('adoption-start-date').value;
      const endDate = document.getElementById('adoption-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      // Get selected additional columns
      const selectedColumns = Array.from(document.querySelectorAll('.column-btn.active')).map(btn => btn.getAttribute('data-column'));

      // Get selected animal filter if any
      const animalSearchInput = document.getElementById('adoption-animal-search');
      const selectedAnimalName = animalSearchInput.value.trim();

      const params = new URLSearchParams({
        filter: filterType,
        startDate: startDate,
        endDate: endDate,
        columns: selectedColumns.join(','),
        animalFilter: selectedAnimalName
      });

      // Open adoptions donations report in new window
      window.open(`adoptions-donations-report.html?${params.toString()}`, '_blank');
    }

    // Generate Document Compliance Report
    function generateDocumentComplianceReport() {
      const activeFilter = document.querySelector('.document-compliance-report-builder .filter-btn.active');
      const filterType = activeFilter ? activeFilter.getAttribute('data-filter') : 'all';

      // Get selected columns
      const selectedColumns = [];
      document.querySelectorAll('.document-compliance-report-builder .column-btn.active').forEach(btn => {
        selectedColumns.push(btn.getAttribute('data-column'));
      });

      if (selectedColumns.length === 0) {
        alert('Please select at least one column to include in the report.');
        return;
      }

      const params = new URLSearchParams({
        filter: filterType,
        columns: selectedColumns.join(',')
      });

      // Open document compliance report in new window
      window.open(`document-compliance-report.html?${params.toString()}`, '_blank');
    }

    // Generate Animal Deaths Report
    function generateAnimalDeathsReport() {
      const startDate = document.getElementById('deaths-start-date').value;
      const endDate = document.getElementById('deaths-end-date').value;

      if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
      }

      // Get selected columns
      const selectedColumns = [];
      document.querySelectorAll('.animal-deaths-report-builder .column-btn.active').forEach(btn => {
        selectedColumns.push(btn.getAttribute('data-column'));
      });

      if (selectedColumns.length === 0) {
        alert('Please select at least one column to include in the report.');
        return;
      }

      const params = new URLSearchParams({
        startDate: startDate,
        endDate: endDate,
        columns: selectedColumns.join(',')
      });

      // Open animal deaths report in new window
      window.open(`animal-deaths-report.html?${params.toString()}`, '_blank');
    }
  </script>
</body>
</html>
