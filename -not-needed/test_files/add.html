<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Add New Animal</title>
  <link rel="stylesheet" href="styles.css" />
  <style>
    body {
      margin: 0;
      padding: 0;
      background: transparent;
    }

    .form-wrapper {
      margin: 0;
      padding: 0;
      background: transparent;
      box-shadow: none;
    }

    .form-wrapper h1 {
      margin-top: 0;
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }

    .form-wrapper form {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .form-wrapper label {
      display: flex;
      flex-direction: column;
      font-weight: bold;
    }

    .form-wrapper input,
    .form-wrapper select,
    .form-wrapper textarea {
      padding: 0.6rem;
      border-radius: 8px;
      border: 1px solid #ccc;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      font-size: 1rem;
    }

    .form-wrapper button[type="submit"] {
      padding: 0.6rem 1.2rem;
      background-color: #446c35;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: bold;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      width: fit-content;
    }

    .form-wrapper button[type="submit"]:hover {
      background-color: #365b2b;
    }
  </style>
</head>
<body>
  <main class="form-wrapper">
    <h1>Add New Animal</h1>
    <form id="animal-form">
      <label>
        Name:
        <input type="text" name="name" required />
      </label>

      <label>
        Species:
        <input type="text" name="species" required />
      </label>

      <label>
        Status:
        <select name="status" required>
          <option value="">--Select--</option>
          <option value="All Clear">All Clear</option>
          <option value="Under Observation">Under Observation</option>
          <option value="Unwell">Unwell</option>
          <option value="Vet Booked">Vet Booked</option>
          <option value="In Treatment">In Treatment</option>
          <option value="Recovery">Recovery</option>
          <option value="Ongoing Condition">Ongoing Condition</option>
          <option value="Palliative">Palliative</option>
          <option value="Quarantined">Quarantined</option>
          <option value="Transferred">Transferred</option>
          <option value="Deceased">Deceased</option>
        </select>
      </label>

      <label>
        Photo:
        <div style="margin-bottom: 0.5rem;">
          <button type="button" id="upload-tab" onclick="switchPhotoMethod('upload')" style="background: #446c35; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px 0 0 4px; cursor: pointer;">Upload File</button>
          <button type="button" id="url-tab" onclick="switchPhotoMethod('url')" style="background: #ccc; color: #666; border: none; padding: 0.5rem 1rem; border-radius: 0 4px 4px 0; cursor: pointer;">Enter URL</button>
        </div>

        <div id="upload-method" style="display: block;">
          <input type="file" name="photo" accept="image/*" id="photo-upload" />
          <small style="color: #666; font-size: 0.9rem;">Choose an image file (JPG, PNG, etc.)</small>
        </div>

        <div id="url-method" style="display: none;">
          <input type="url" name="photo_url" id="photo-url" placeholder="https://example.com/image.jpg" />
          <small style="color: #666; font-size: 0.9rem;">Enter a direct link to an image</small>
        </div>
      </label>

      <div id="photo-preview" style="display: none; margin-top: 0.5rem;">
        <img id="preview-image" style="max-width: 200px; max-height: 200px; border-radius: 8px; border: 2px solid #ddd;" />
        <button type="button" onclick="removePhoto()" style="display: block; margin-top: 0.5rem; background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Remove Photo</button>
      </div>

      <label>
        Notes:
        <textarea name="notes" rows="4"></textarea>
      </label>

      <button type="submit">Save Animal</button>
    </form>
  </main>

  <!-- Photo handling functions -->
  <script>
    // Switch between upload and URL methods
    function switchPhotoMethod(method) {
      const uploadTab = document.getElementById('upload-tab');
      const urlTab = document.getElementById('url-tab');
      const uploadMethod = document.getElementById('upload-method');
      const urlMethod = document.getElementById('url-method');

      if (method === 'upload') {
        uploadTab.style.background = '#446c35';
        uploadTab.style.color = 'white';
        urlTab.style.background = '#ccc';
        urlTab.style.color = '#666';
        uploadMethod.style.display = 'block';
        urlMethod.style.display = 'none';
        // Clear URL input
        document.getElementById('photo-url').value = '';
      } else {
        urlTab.style.background = '#446c35';
        urlTab.style.color = 'white';
        uploadTab.style.background = '#ccc';
        uploadTab.style.color = '#666';
        urlMethod.style.display = 'block';
        uploadMethod.style.display = 'none';
        // Clear file input
        document.getElementById('photo-upload').value = '';
      }
      // Clear preview
      document.getElementById('photo-preview').style.display = 'none';
    }

    // Photo preview functionality for file upload
    document.getElementById('photo-upload').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          document.getElementById('preview-image').src = e.target.result;
          document.getElementById('photo-preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
      }
    });

    // Photo preview functionality for URL input
    document.getElementById('photo-url').addEventListener('input', function(e) {
      const url = e.target.value;
      if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
        document.getElementById('preview-image').src = url;
        document.getElementById('photo-preview').style.display = 'block';
      } else {
        document.getElementById('photo-preview').style.display = 'none';
      }
    });

    function removePhoto() {
      document.getElementById('photo-upload').value = '';
      document.getElementById('photo-url').value = '';
      document.getElementById('photo-preview').style.display = 'none';
    }

    // Convert file to base64
    function fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
      });
    }
  </script>

  <script type="module">
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const form = document.getElementById('animal-form');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(form);
      const data = Object.fromEntries(formData.entries());

      // Handle photo - either file upload or URL
      const photoFile = formData.get('photo');
      const photoUrl = formData.get('photo_url');

      if (photoFile && photoFile.size > 0) {
        // File upload method
        try {
          const base64 = await fileToBase64(photoFile);
          data.photo_url = base64;
        } catch (error) {
          console.error('Error processing photo:', error);
          alert('Error processing photo. Please try again.');
          return;
        }
      } else if (photoUrl && photoUrl.trim()) {
        // URL method
        data.photo_url = photoUrl.trim();
      }

      // Remove the photo file from data since we're using photo_url
      delete data.photo;

      const res = await fetch(`${SUPABASE_URL}/rest/v1/animals`, {
        method: 'POST',
        headers: {
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`,
          'Content-Type': 'application/json',
          Prefer: 'return=representation'
        },
        body: JSON.stringify(data)
      });

      const json = await res.json();
      if (res.ok && json[0]) {
        const newId = json[0].id;
        window.location.href = `animal.html?id=${newId}`;
      } else {
        alert("Something went wrong");
        console.error("Supabase error:", res.status, json);
      }
    });
  </script>
</body>
</html>