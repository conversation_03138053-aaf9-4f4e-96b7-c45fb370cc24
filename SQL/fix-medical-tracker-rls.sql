-- Temporary fix for medical tracker RLS policies
-- This allows authenticated users to access medical data while we set up proper user roles

-- Drop existing restrictive policies for medical tables
DROP POLICY IF EXISTS "Allow authenticated users to read medical episodes" ON medical_episodes;
DROP POLICY IF EXISTS "Allow staff to manage medical episodes" ON medical_episodes;
DROP POLICY IF EXISTS "Ad<PERSON> and Staff can manage medical episodes" ON medical_episodes;
DROP POLICY IF EXISTS "Volunteers can view open medical episodes" ON medical_episodes;

DROP POLICY IF EXISTS "Allow authenticated users to read medical interventions" ON medical_interventions;
DROP POLICY IF EXISTS "Allow staff to manage medical interventions" ON medical_interventions;

-- Create simple, permissive policies for testing
CREATE POLICY "Allow all authenticated users to read medical episodes" ON medical_episodes
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all authenticated users to manage medical episodes" ON medical_episodes
  FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow all authenticated users to read medical interventions" ON medical_interventions
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all authenticated users to manage medical interventions" ON medical_interventions
  FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');

-- Also ensure the user can read animals (needed for medical tracker)
DROP POLICY IF EXISTS "Allow authenticated users to read animals" ON animals;
CREATE POLICY "Allow all authenticated users to read animals" ON animals
  FOR SELECT USING (auth.role() = 'authenticated');

-- Grant permissions to authenticated users
GRANT ALL ON medical_episodes TO authenticated;
GRANT ALL ON medical_interventions TO authenticated;
GRANT SELECT ON animals TO authenticated;

-- Add some test data for Mika (animal ID 3) if it doesn't exist
-- First, let's check if Mika exists and get her ID
DO $$
DECLARE
    mika_id UUID;
    episode_id UUID;
BEGIN
    -- Get Mika's ID (assuming she's the animal named 'Mika')
    SELECT id INTO mika_id FROM animals WHERE name = 'Mika' LIMIT 1;
    
    IF mika_id IS NOT NULL THEN
        -- Check if medical episode already exists
        SELECT id INTO episode_id FROM medical_episodes WHERE animal_id = mika_id LIMIT 1;
        
        IF episode_id IS NULL THEN
            -- Create a test medical episode for Mika
            INSERT INTO medical_episodes (
                animal_id, 
                title, 
                type, 
                severity, 
                description, 
                date_discovered, 
                status
            ) VALUES (
                mika_id,
                'Broken Wing',
                'injury',
                'high',
                'Left wing appears to be fractured, bird unable to fly properly',
                '2024-01-15',
                'active'
            ) RETURNING id INTO episode_id;
            
            -- Add some test interventions
            INSERT INTO medical_interventions (
                episode_id,
                intervention_date,
                intervention_type,
                description,
                staff_member,
                outcome,
                next_steps
            ) VALUES 
            (
                episode_id,
                '2024-01-15 10:00:00+00',
                'examination',
                'Initial examination revealed fracture in left wing. X-ray taken.',
                'Dr. Smith',
                'Fracture confirmed in radius bone',
                'Immobilize wing and start pain medication'
            ),
            (
                episode_id,
                '2024-01-16 09:00:00+00',
                'treatment',
                'Wing splinted and bandaged. Pain medication administered.',
                'Nurse Johnson',
                'Wing successfully immobilized',
                'Monitor for 48 hours, check bandage daily'
            ),
            (
                episode_id,
                '2024-01-18 14:00:00+00',
                'monitoring',
                'Bandage changed, wing healing well. Bird showing improved appetite.',
                'Dr. Smith',
                'Good healing progress, no signs of infection',
                'Continue monitoring, reduce pain medication'
            );
            
            RAISE NOTICE 'Created test medical episode and interventions for Mika';
        ELSE
            RAISE NOTICE 'Medical episode already exists for Mika';
        END IF;
    ELSE
        RAISE NOTICE 'Mika not found in animals table';
    END IF;
END $$;
