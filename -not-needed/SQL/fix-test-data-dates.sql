-- Fix Test Data Dates for Chart Testing
-- This script updates the created_at timestamps to match the start_date
-- This makes logical sense as the record should be created when the adoption/donation starts

-- Update created_at to match start_date (with a random time during that day)
UPDATE adoptions_donations
SET created_at = start_date + (RANDOM() * INTERVAL '23 hours 59 minutes')
WHERE start_date IS NOT NULL;

-- Optional: If you want to also update start_date to be spread across months
-- Uncomment the section below to distribute start_dates across the last 6 months

/*
-- Update start_dates to be distributed across the last 6 months
WITH numbered_records AS (
  SELECT
    id,
    ROW_NUMBER() OVER (ORDER BY id) as row_num,
    COUNT(*) OVER () as total_count
  FROM adoptions_donations
)
UPDATE adoptions_donations
SET
  start_date = CURRENT_DATE - INTERVAL '6 months' +
    (INTERVAL '6 months' * (nr.row_num - 1) / nr.total_count) +
    (RANDOM() * INTERVAL '7 days'), -- Add some randomness within the week
  created_at = start_date + (RANDOM() * INTERVAL '23 hours 59 minutes')
FROM numbered_records nr
WHERE adoptions_donations.id = nr.id;
*/

-- Verify the distribution
SELECT 
  DATE_TRUNC('month', created_at) as month,
  donation_type,
  COUNT(*) as count
FROM adoptions_donations 
GROUP BY DATE_TRUNC('month', created_at), donation_type
ORDER BY month, donation_type;
