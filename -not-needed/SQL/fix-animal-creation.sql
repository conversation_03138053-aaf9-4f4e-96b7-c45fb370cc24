-- Fix Animal Creation Issues
-- Run this in Supabase SQL Editor to fix RLS and storage issues

-- 1. First, let's check current RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'animals';

-- 2. Temporarily disable <PERSON><PERSON> to test if that's the issue
-- (You can re-enable it later with proper policies)
ALTER TABLE animals DISABLE ROW LEVEL SECURITY;

-- 3. Check if the status values in the form match the database
-- Let's see what status values currently exist
SELECT DISTINCT status FROM animals;

-- 4. If you want to re-enable RLS later, use these simple policies:
-- First drop existing policies
DROP POLICY IF EXISTS "authenticated_users_can_read_animals" ON animals;
DROP POLICY IF EXISTS "Allow authenticated users to read animals" ON animals;
DROP POLICY IF EXISTS "Allow authenticated users to insert animals" ON animals;
DROP POLICY IF EXISTS "Allow authenticated users to update animals" ON animals;

-- Create simple policies that work
CREATE POLICY "allow_authenticated_select" ON animals
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "allow_authenticated_insert" ON animals
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "allow_authenticated_update" ON animals
  FOR UPDATE USING (auth.role() = 'authenticated');

-- 5. Re-enable RLS with the new policies
ALTER TABLE animals ENABLE ROW LEVEL SECURITY;

-- 6. Test insert (replace with actual values)
-- INSERT INTO animals (name, species, status, "Group", notes, created_by) 
-- VALUES ('Test Bird', 'Test Species', 'All Clear', 'Owls', 'Test notes', auth.uid());

-- 7. Grant necessary permissions
GRANT ALL ON animals TO authenticated;
GRANT ALL ON animals TO anon;

-- 8. Check if there are any triggers causing issues
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers
WHERE event_object_table = 'animals';

-- 9. Create the storage bucket (this needs to be done via Supabase dashboard or API)
-- Go to Supabase Dashboard > Storage > Create Bucket
-- Name: animal-photos
-- Public: Yes
-- File size limit: 5MB
-- Allowed MIME types: image/jpeg, image/png, image/webp, image/gif
