<!DOCTYPE html>
<html lang="en">
<head>
  <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Today's Logs Tracker - Cornish Birds of Prey Center</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 10;
      padding: 1.5rem;
      background: #4285a6;
      background: linear-gradient(47deg, rgb(127, 186, 215) 0%, rgb(162, 210, 163) 59%, rgb(253, 246, 181) 100%);
      background-attachment: fixed;
      min-height: 100vh;
      color: #333;
      position: relative;
    }

     .report-container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(15px);
      border-radius: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      padding: 30px;
    }

    .report-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid rgba(66, 133, 166, 0.3);
      padding-bottom: 20px;
    }

    .report-title {
      font-size: 2.5rem;
      color: #2c3e50;
      margin-bottom: 10px;
      font-weight: 700;
    }

    .report-subtitle {
      font-size: 1.2rem;
      color: #7f8c8d;
      margin-bottom: 10px;
    }

    .report-date {
      font-size: 1rem;
      color: #95a5a6;
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: bold;
      color: #2980b9;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #7f8c8d;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .tracker-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .tracker-table th {
      background: #8bbccf;
      color: #2c3e50;
      padding: 15px 10px;
      text-align: center;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-right: 1px solid rgba(255, 255, 255, 0.3);
    }

    .tracker-table th:last-child {
      border-right: none;
    }

    .tracker-table td {
      padding: 12px 10px;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      border-right: 1px solid rgba(0, 0, 0, 0.1);
      font-size: 0.9rem;
    }

    .tracker-table td:last-child {
      border-right: none;
    }

    .tracker-table tbody tr:hover {
      background: rgba(66, 133, 166, 0.1);
    }

    .animal-name {
      font-weight: 600;
      color: #2c3e50;
      text-align: left !important;
      padding-left: 15px !important;
    }

    .status-y {
      color: #27ae60;
      font-weight: bold;
    }

    .status-n {
      color: #e74c3c;
      font-weight: bold;
    }

    .status-dash {
      color: #95a5a6;
    }

    .medical-episode {
      font-size: 0.8rem;
      color: #e67e22;
      font-weight: 500;
    }

    .notes-cell {
      max-width: 200px;
      text-align: left !important;
      font-size: 0.8rem;
      color: #34495e;
    }

    .staff-cell {
      font-size: 0.8rem;
      color: #7f8c8d;
      font-style: italic;
    }

    .floating-buttons {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      z-index: 1000;
    }

    .floating-btn {
      background: rgba(66, 133, 166, 0.9);
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .close-btn {
      background: rgba(66, 133, 166, 0.9);
    }

    .close-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .no-data {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 2rem;
    }

    .loading {
      text-align: center;
      color: #666;
      padding: 2rem;
    }

    /* Print styles */
    @media print {
      .floating-buttons {
        display: none !important;
      }
      
      body {
        background: white !important;
        padding: 0 !important;
      }
      
      .report-container {
        background: white !important;
        backdrop-filter: none !important;
        border: none !important;
        box-shadow: none !important;
        padding: 20px !important;
      }
      
      .stat-card {
        background: white !important;
        backdrop-filter: none !important;
        border: 1px solid #ddd !important;
      }
      
      .tracker-table {
        background: white !important;
      }
    }

    /* Responsive Design */

    /* Mobile (320px - 768px) */
    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      .report-container {
        padding: 15px;
        margin: 0;
        border-radius: 15px;
      }

      .report-title {
        font-size: 1.8rem;
      }

      .report-subtitle {
        font-size: 1rem;
      }

      .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
      }

      .stat-card {
        padding: 15px;
      }

      .stat-number {
        font-size: 2rem;
      }

      .stat-label {
        font-size: 0.8rem;
      }

      .tracker-table {
        font-size: 0.75rem;
        display: block;
        overflow-x: auto;
        white-space: nowrap;
      }

      .tracker-table th,
      .tracker-table td {
        padding: 8px 4px;
        min-width: 80px;
      }

      .animal-name {
        min-width: 100px !important;
        padding-left: 8px !important;
      }

      .notes-cell {
        max-width: 150px;
        font-size: 0.7rem;
      }

      .medical-episode {
        font-size: 0.7rem;
      }

      .staff-cell {
        font-size: 0.7rem;
      }

      .floating-buttons {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-bottom: 20px;
        gap: 8px;
      }

      .floating-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
      }
    }

    /* Tablet Portrait (768px - 1024px) */
    @media (min-width: 768px) and (max-width: 1024px) {
      .report-container {
        padding: 20px;
      }

      .report-title {
        font-size: 2.2rem;
      }

      .summary-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 18px;
      }

      .stat-card {
        padding: 18px;
      }

      .stat-number {
        font-size: 2.2rem;
      }

      .tracker-table {
        font-size: 0.85rem;
      }

      .tracker-table th,
      .tracker-table td {
        padding: 10px 8px;
      }

      .floating-buttons {
        top: 15px;
        right: 15px;
      }

      .floating-btn {
        padding: 11px 18px;
        font-size: 0.85rem;
      }
    }

    /* Tablet Landscape (1024px - 1200px) */
    @media (min-width: 1024px) and (max-width: 1200px) {
      .report-container {
        padding: 25px;
      }

      .summary-stats {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      }

      .tracker-table {
        font-size: 0.9rem;
      }
    }

    /* Small Mobile (320px - 480px) */
    @media (max-width: 480px) {
      body {
        padding: 0.5rem;
      }

      .report-container {
        padding: 10px;
        border-radius: 10px;
      }

      .report-title {
        font-size: 1.5rem;
      }

      .report-subtitle {
        font-size: 0.9rem;
      }

      .summary-stats {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .stat-card {
        padding: 12px;
      }

      .stat-number {
        font-size: 1.8rem;
      }

      .stat-label {
        font-size: 0.75rem;
      }

      .tracker-table {
        font-size: 0.7rem;
      }

      .tracker-table th,
      .tracker-table td {
        padding: 6px 3px;
        min-width: 70px;
      }

      .animal-name {
        min-width: 90px !important;
        padding-left: 6px !important;
      }

      .notes-cell {
        max-width: 120px;
        font-size: 0.65rem;
      }

      .floating-buttons {
        flex-direction: column;
        gap: 5px;
      }

      .floating-btn {
        padding: 8px 12px;
        font-size: 0.75rem;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Action Buttons -->
  <div class="floating-buttons">
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
    <button class="floating-btn close-btn" onclick="closeWindow()">
      <span class="material-icons">close</span>
      Close Window
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title" id="report-title">Today's Logs Tracker</h1>
      <p class="report-subtitle" id="report-subtitle">Loading...</p>
      <p class="report-date" id="report-date"></p>
    </div>

    <div class="summary-stats" id="summary-stats">
      <!-- Summary statistics will be populated here -->
    </div>

    <div id="tracker-content">
      <div class="loading">Loading today's tracking data...</div>
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Initialize report
    document.addEventListener('DOMContentLoaded', async () => {
      await checkAuthAndInitialize();
    });

    async function checkAuthAndInitialize() {
      try {
        console.log('Checking authentication...');

        // Check for existing session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        console.log('Session check result:', { session: !!session, error: sessionError });

        if (sessionError) {
          console.error('Session error:', sessionError);
          document.getElementById('tracker-content').innerHTML = '<div class="no-data">Authentication error. Please login again.</div>';
          return;
        }

        if (!session) {
          console.log('No active session, redirecting to login');
          window.location.href = 'login.html';
          return;
        }

        console.log('✅ Authenticated user:', session.user.email);
        await initializeReport();

      } catch (error) {
        console.error('Auth check error:', error);
        document.getElementById('tracker-content').innerHTML = '<div class="no-data">Error checking authentication.</div>';
      }
    }

    async function initializeReport() {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      const isAM = today.getHours() < 12;
      
      // Update report title and subtitle
      document.getElementById('report-title').textContent = `Today's Logs Tracker - ${isAM ? 'AM' : 'PM'}`;
      document.getElementById('report-subtitle').textContent = `Daily Care Tracking Report`;
      document.getElementById('report-date').textContent = today.toLocaleDateString('en-GB', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });

      await loadTodayTrackerData(todayStr);
    }

    async function loadTodayTrackerData(todayStr) {
      try {
        // First try to load all animals to debug
        console.log('Loading animals...');
        const { data: allAnimals, error: allAnimalsError } = await supabase
          .from('animals')
          .select('id, name, species, Group, status');

        console.log('All animals query result:', { data: allAnimals, error: allAnimalsError });

        if (allAnimalsError) {
          console.error('Error loading all animals:', allAnimalsError);
          document.getElementById('tracker-content').innerHTML = '<div class="no-data">Error loading animals data: ' + allAnimalsError.message + '</div>';
          return;
        }

        // Filter out Deceased and Transferred animals
        const animals = allAnimals ? allAnimals.filter(animal =>
          animal.status !== 'Deceased' && animal.status !== 'Transferred'
        ) : [];

        console.log('Filtered animals:', animals);

        // Load today's daily logs
        const { data: todayLogs, error: logsError } = await supabase
          .from('daily_logs')
          .select('*')
          .gte('created_on', todayStr + 'T00:00:00')
          .lt('created_on', todayStr + 'T23:59:59')
          .order('created_on', { ascending: false });

        if (logsError) {
          console.error('Error loading daily logs:', logsError);
          document.getElementById('tracker-content').innerHTML = '<div class="no-data">Error loading daily logs data.</div>';
          return;
        }

        // Load active medical episodes
        const { data: medicalEpisodes, error: medicalError } = await supabase
          .from('medical_episodes')
          .select('animal_id, title, status')
          .eq('status', 'active');

        if (medicalError) {
          console.error('Error loading medical episodes:', medicalError);
        }

        // Process and display data
        await processTrackerData(animals, todayLogs || [], medicalEpisodes || []);

      } catch (error) {
        console.error('Error loading tracker data:', error);
        document.getElementById('tracker-content').innerHTML = '<div class="no-data">Error loading tracker data.</div>';
      }
    }

    async function processTrackerData(animals, todayLogs, medicalEpisodes) {
      // Create lookup maps
      const logsByAnimal = {};
      const medicalByAnimal = {};

      // Group logs by animal
      todayLogs.forEach(log => {
        if (!logsByAnimal[log.animal_id]) {
          logsByAnimal[log.animal_id] = [];
        }
        logsByAnimal[log.animal_id].push(log);
      });

      // Group medical episodes by animal
      medicalEpisodes.forEach(episode => {
        if (!medicalByAnimal[episode.animal_id]) {
          medicalByAnimal[episode.animal_id] = [];
        }
        medicalByAnimal[episode.animal_id].push(episode);
      });

      // Calculate summary statistics
      calculateSummaryStats(animals, logsByAnimal);

      // Generate tracker table
      generateTrackerTable(animals, logsByAnimal, medicalByAnimal);
    }

    function calculateSummaryStats(animals, logsByAnimal) {
      const totalAnimals = animals.length;
      const animalsWithLogs = Object.keys(logsByAnimal).length;

      let totalFedAM = 0, totalFedPM = 0, totalCleanedAM = 0, totalCleanedPM = 0;

      Object.values(logsByAnimal).forEach(logs => {
        const fedAM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.fed === 'Yes' || log.fed === true) && logTime.getHours() < 12;
        });
        const fedPM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.fed === 'Yes' || log.fed === true) && logTime.getHours() >= 12;
        });
        const cleanedAM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.cleaned === 'Yes' || log.cleaned === true) && logTime.getHours() < 12;
        });
        const cleanedPM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.cleaned === 'Yes' || log.cleaned === true) && logTime.getHours() >= 12;
        });

        if (fedAM) totalFedAM++;
        if (fedPM) totalFedPM++;
        if (cleanedAM) totalCleanedAM++;
        if (cleanedPM) totalCleanedPM++;
      });

      const summaryHTML = `
        <div class="stat-card">
          <div class="stat-number">${totalAnimals}</div>
          <div class="stat-label">Total Animals</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${animalsWithLogs}</div>
          <div class="stat-label">Animals with Logs</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${totalFedAM}</div>
          <div class="stat-label">Fed AM</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${totalFedPM}</div>
          <div class="stat-label">Fed PM</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${totalCleanedAM}</div>
          <div class="stat-label">Cleaned AM</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${totalCleanedPM}</div>
          <div class="stat-label">Cleaned PM</div>
        </div>
      `;

      document.getElementById('summary-stats').innerHTML = summaryHTML;
    }

    function generateTrackerTable(animals, logsByAnimal, medicalByAnimal) {
      let tableHTML = `
        <table class="tracker-table">
          <thead>
            <tr>
              <th>Animal Name</th>
              <th>Fed AM</th>
              <th>Fed PM</th>
              <th>Cleaned AM</th>
              <th>Cleaned PM</th>
              <th>Live Medical Episode</th>
              <th>Notes</th>
              <th>Member of Staff</th>
            </tr>
          </thead>
          <tbody>
      `;

      animals.forEach(animal => {
        const logs = logsByAnimal[animal.id] || [];
        const medicalEpisodes = medicalByAnimal[animal.id] || [];

        // Check feeding status
        const fedAM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.fed === 'Yes' || log.fed === true) && logTime.getHours() < 12;
        });
        const fedPM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.fed === 'Yes' || log.fed === true) && logTime.getHours() >= 12;
        });

        // Check cleaning status
        const cleanedAM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.cleaned === 'Yes' || log.cleaned === true) && logTime.getHours() < 12;
        });
        const cleanedPM = logs.some(log => {
          const logTime = new Date(log.created_on);
          return (log.cleaned === 'Yes' || log.cleaned === true) && logTime.getHours() >= 12;
        });

        // Get medical episodes
        const medicalText = medicalEpisodes.length > 0
          ? medicalEpisodes.map(ep => `${ep.title} (${ep.status})`).join(', ')
          : 'None';

        // Get most recent notes
        const notesLog = logs.find(log => log.notes && log.notes.trim());
        const notesText = notesLog ? notesLog.notes : 'No notes';

        // Get staff member from most recent log
        const staffLog = logs.length > 0 ? logs[0] : null;
        const staffText = staffLog ? (staffLog.logged_by || 'Unknown Staff') : 'No logs today';

        // Format status values
        const formatStatus = (hasLogs, status) => {
          if (!hasLogs) return '<span class="status-dash">-</span>';
          return status ? '<span class="status-y">Y</span>' : '<span class="status-n">N</span>';
        };

        tableHTML += `
          <tr>
            <td class="animal-name">${animal.name}</td>
            <td>${formatStatus(logs.length > 0, fedAM)}</td>
            <td>${formatStatus(logs.length > 0, fedPM)}</td>
            <td>${formatStatus(logs.length > 0, cleanedAM)}</td>
            <td>${formatStatus(logs.length > 0, cleanedPM)}</td>
            <td class="medical-episode">${medicalText}</td>
            <td class="notes-cell">${notesText}</td>
            <td class="staff-cell">${staffText}</td>
          </tr>
        `;
      });

      tableHTML += '</tbody></table>';

      document.getElementById('tracker-content').innerHTML = tableHTML;
    }

    // Close window function - make it globally accessible
    function closeWindow() {
      window.location.href = 'reports.html';
    }

    // Make closeWindow globally accessible
    window.closeWindow = closeWindow;
  </script>
</body>
</html>
