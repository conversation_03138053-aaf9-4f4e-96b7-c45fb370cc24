<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Animal Deaths Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    /* Animal Deaths Report Specific Styles */
    .report-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      background: white;
      min-height: 100vh;
    }

    .report-header {
      text-align: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 3px solid #dc3545;
    }

    .report-title {
      color: #dc3545;
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0;
      text-shadow: 1px 1px 2px rgba(220, 53, 69, 0.1);
    }

    .report-subtitle {
      color: #666;
      font-size: 1.2rem;
      margin: 0.5rem 0 0 0;
    }

    .report-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 1.5rem 0;
      padding: 1rem;
      background: rgba(220, 53, 69, 0.05);
      border-radius: 8px;
      border-left: 4px solid #dc3545;
    }

    .date-range {
      font-weight: 600;
      color: #2c3e50;
    }

    .record-count {
      font-weight: 600;
      color: #dc3545;
    }

    .deaths-table {
      width: 100%;
      border-collapse: collapse;
      margin: 2rem 0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      overflow: hidden;
    }

    .deaths-table thead {
      background: linear-gradient(135deg, #dc3545, #c82333);
      color: white;
    }

    .deaths-table th {
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      font-size: 1rem;
      border-right: 1px solid rgba(255, 255, 255, 0.2);
    }

    .deaths-table th:last-child {
      border-right: none;
    }

    .deaths-table tbody tr {
      background: white;
      border-bottom: 1px solid #eee;
      transition: background-color 0.2s ease;
    }

    .deaths-table tbody tr:nth-child(even) {
      background: rgba(220, 53, 69, 0.02);
    }

    .deaths-table tbody tr:hover {
      background: rgba(220, 53, 69, 0.08);
    }

    .deaths-table td {
      padding: 1rem;
      border-right: 1px solid #eee;
      vertical-align: top;
    }

    .deaths-table td:last-child {
      border-right: none;
    }

    .no-records {
      text-align: center;
      padding: 3rem;
      color: #666;
      font-size: 1.2rem;
      background: rgba(220, 53, 69, 0.05);
      border-radius: 8px;
      border: 2px dashed #dc3545;
    }

    .no-records .material-icons {
      font-size: 3rem;
      color: #dc3545;
      margin-bottom: 1rem;
      display: block;
    }

    .print-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 1000;
    }

    .print-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    .print-btn:hover {
      background: #c82333;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    }

    /* Print styles */
    @media print {
      .print-controls {
        display: none !important;
      }
      
      .report-container {
        padding: 0;
        max-width: none;
      }
      
      .deaths-table {
        page-break-inside: auto;
      }
      
      .deaths-table thead {
        display: table-header-group;
      }
      
      .deaths-table tbody tr {
        page-break-inside: avoid;
      }
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .report-container {
        padding: 1rem;
      }
      
      .report-title {
        font-size: 2rem;
      }
      
      .report-meta {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
      }
      
      .deaths-table {
        font-size: 0.9rem;
      }
      
      .deaths-table th,
      .deaths-table td {
        padding: 0.75rem 0.5rem;
      }
      
      .print-controls {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 1rem;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="print-controls">
    <button class="print-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="print-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Animal Deaths Report</h1>
      <p class="report-subtitle">Cornish Birds of Prey Center</p>
    </div>

    <div class="report-meta">
      <div class="date-range">
        Date Range: <span id="date-range-display">Loading...</span>
      </div>
      <div class="record-count">
        Total Records: <span id="record-count">0</span>
      </div>
    </div>

    <div id="report-content">
      <!-- Table will be populated by JavaScript -->
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const startDate = urlParams.get('startDate');
    const endDate = urlParams.get('endDate');
    const columns = urlParams.get('columns') ? urlParams.get('columns').split(',') : ['name', 'species', 'gender', 'age', 'date_of_death', 'reason_for_death'];

    // Column display names
    const columnNames = {
      name: 'Name',
      species: 'Species',
      gender: 'Gender',
      age: 'Age',
      date_of_death: 'Date of Death',
      reason_for_death: 'Reason for Death'
    };

    // Load and display the report
    async function loadReport() {
      try {
        // Update date range display
        const startDateFormatted = new Date(startDate).toLocaleDateString('en-GB', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        const endDateFormatted = new Date(endDate).toLocaleDateString('en-GB', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
        document.getElementById('date-range-display').textContent = `${startDateFormatted} - ${endDateFormatted}`;

        // Query animals with deaths in the date range
        const { data: animals, error } = await supabase
          .from('animals')
          .select('*')
          .not('date_of_death', 'is', null)
          .gte('date_of_death', startDate)
          .lte('date_of_death', endDate)
          .order('date_of_death', { ascending: false });

        if (error) {
          throw error;
        }

        // Update record count
        document.getElementById('record-count').textContent = animals.length;

        // Generate report content
        generateReportTable(animals);

      } catch (error) {
        console.error('Error loading report:', error);
        document.getElementById('report-content').innerHTML = `
          <div class="no-records">
            <span class="material-icons">error</span>
            Error loading report data. Please try again.
          </div>
        `;
      }
    }

    // Generate the report table
    function generateReportTable(animals) {
      const reportContent = document.getElementById('report-content');

      if (animals.length === 0) {
        reportContent.innerHTML = `
          <div class="no-records">
            <span class="material-icons">pets</span>
            No deaths recorded for this period.
          </div>
        `;
        return;
      }

      // Create table
      let tableHTML = `
        <table class="deaths-table">
          <thead>
            <tr>
              ${columns.map(col => `<th>${columnNames[col]}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
      `;

      animals.forEach(animal => {
        tableHTML += '<tr>';
        columns.forEach(col => {
          let value = animal[col] || '';
          
          // Format date of death
          if (col === 'date_of_death' && value) {
            value = new Date(value).toLocaleDateString('en-GB', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            });
          }
          
          tableHTML += `<td>${value}</td>`;
        });
        tableHTML += '</tr>';
      });

      tableHTML += `
          </tbody>
        </table>
      `;

      reportContent.innerHTML = tableHTML;
    }

    // Initialize the report
    loadReport();
  </script>
</body>
</html>
