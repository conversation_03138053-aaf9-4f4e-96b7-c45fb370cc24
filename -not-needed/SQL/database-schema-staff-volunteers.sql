-- Staff & Volunteers Management Module Database Schema
-- For Cornish Birds of Prey Center Application

-- Create the staff_volunteers table
CREATE TABLE staff_volunteers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('Staff', 'Volunteer')),
  position TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone_number TEXT,
  start_date DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive')),
  photo_url TEXT,
  last_login TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add comments for documentation
COMMENT ON TABLE staff_volunteers IS 'Staff and volunteer management for Cornish Birds of Prey Center';
COMMENT ON COLUMN staff_volunteers.id IS 'Unique identifier for each person';
COMMENT ON COLUMN staff_volunteers.name IS 'Full name of staff member or volunteer';
COMMENT ON COLUMN staff_volunteers.role IS 'Either Staff or Volunteer';
COMMENT ON COLUMN staff_volunteers.position IS 'Job title or volunteer role';
COMMENT ON COLUMN staff_volunteers.email IS 'Email address (must be unique)';
COMMENT ON COLUMN staff_volunteers.phone_number IS 'Contact phone number (optional)';
COMMENT ON COLUMN staff_volunteers.start_date IS 'Date when person started';
COMMENT ON COLUMN staff_volunteers.status IS 'Active or Inactive status';
COMMENT ON COLUMN staff_volunteers.photo_url IS 'URL to profile photo (optional)';
COMMENT ON COLUMN staff_volunteers.last_login IS 'Last login timestamp (if applicable)';

-- Enable Row Level Security
ALTER TABLE staff_volunteers ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (adjust based on your authentication needs)
-- For now, allowing all operations - you may want to restrict based on user roles
CREATE POLICY "Allow all operations on staff_volunteers" ON staff_volunteers
  FOR ALL USING (true);

-- Create function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on row updates
CREATE TRIGGER update_staff_volunteers_updated_at 
    BEFORE UPDATE ON staff_volunteers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_staff_volunteers_role ON staff_volunteers(role);
CREATE INDEX idx_staff_volunteers_status ON staff_volunteers(status);
CREATE INDEX idx_staff_volunteers_email ON staff_volunteers(email);
CREATE INDEX idx_staff_volunteers_created_at ON staff_volunteers(created_at);

-- Insert some sample data for testing (optional)
INSERT INTO staff_volunteers (name, role, position, email, phone_number, start_date, status, photo_url) VALUES
('Sarah Johnson', 'Staff', 'Centre Manager', '<EMAIL>', '01234 567890', '2020-01-15', 'Active', NULL),
('Mike Thompson', 'Staff', 'Senior Bird Handler', '<EMAIL>', '01234 567891', '2021-03-10', 'Active', NULL),
('Emma Wilson', 'Volunteer', 'Education Assistant', '<EMAIL>', '07123 456789', '2023-06-01', 'Active', NULL),
('David Brown', 'Volunteer', 'Maintenance Helper', '<EMAIL>', '07123 456788', '2023-04-15', 'Active', NULL),
('Lisa Davis', 'Staff', 'Veterinary Assistant', '<EMAIL>', '01234 567892', '2022-08-20', 'Inactive', NULL);

-- Grant necessary permissions (adjust based on your setup)
-- GRANT ALL ON staff_volunteers TO authenticated;
-- GRANT ALL ON staff_volunteers TO service_role;
