<!DOCTYPE html>
<html lang="en">
<head>
 <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Login – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <!-- Login Form -->
    <main class="login-main">
      <div class="login-container">
        <div class="login-header">
          <h1>Cornish Birds of Prey</h1>
          <p>Staff & Volunteer Access</p>
        </div>

        <!-- Login Form -->
        <div id="login-form-container" class="auth-form-container">
          <form id="login-form" class="auth-form">
            <h2>Sign In</h2>
            
            <div class="form-group">
              <label for="login-email">Email Address</label>
              <input type="email" id="login-email" name="email" required placeholder="<EMAIL>">
            </div>

            <div class="form-group">
              <label for="login-password">Password</label>
              <div class="password-input-container">
                <input type="password" id="login-password" name="password" required placeholder="Enter your password" autocomplete="current-password">
                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('login-password')">
                  <span class="material-icons">visibility</span>
                </button>
              </div>
            </div>

            <div class="form-options">
              <label class="remember-me-label">
                <input type="checkbox" id="remember-me-checkbox" name="remember">
                <span>Remember me</span>
                <span class="remember-me-checkmark"></span>
              </label>
              <a href="#" onclick="showForgotPassword()" class="forgot-password-link">Forgot Password?</a>
            </div>

            <button type="submit" class="btn-primary auth-btn">
              <span class="material-icons">login</span>
              Sign In
            </button>

            <div id="login-feedback" class="feedback"></div>
          </form>

          <!-- Biometric Login Option (shown after first successful login) -->
          <div id="biometric-login" class="biometric-section" style="display: none;">
            <div class="divider">
              <span>or</span>
            </div>
            <button type="button" id="biometric-btn" class="btn-secondary auth-btn">
              <span class="material-icons">fingerprint</span>
              Use Biometric Login
            </button>
          </div>
        </div>

        <!-- Forgot Password Form -->
        <div id="forgot-password-container" class="auth-form-container" style="display: none;">
          <form id="forgot-password-form" class="auth-form">
            <h2>Reset Password</h2>
            <p>Enter your email address and we'll send you a link to reset your password.</p>
            
            <div class="form-group">
              <label for="reset-email">Email Address</label>
              <input type="email" id="reset-email" name="email" required placeholder="<EMAIL>">
            </div>

            <button type="submit" class="btn-primary auth-btn">
              <span class="material-icons">email</span>
              Send Reset Link
            </button>

            <button type="button" class="btn-secondary auth-btn" onclick="showLogin()">
              <span class="material-icons">arrow_back</span>
              Back to Login
            </button>

            <div id="reset-feedback" class="feedback"></div>
          </form>
        </div>

        <!-- 2FA Verification Form -->
        <div id="mfa-container" class="auth-form-container" style="display: none;">
          <form id="mfa-form" class="auth-form">
            <h2>Two-Factor Authentication</h2>
            <p>Enter the 6-digit code from your authenticator app (Google Authenticator, Authy, etc.).</p>

            <div class="form-group">
              <label for="mfa-code">Verification Code</label>
              <input type="text" id="mfa-code" name="code" required placeholder="123456" maxlength="6" pattern="[0-9]{6}" autocomplete="one-time-code">
            </div>

            <button type="submit" class="btn-primary auth-btn">
              <span class="material-icons">verified</span>
              Verify
            </button>

            <button type="button" class="btn-secondary auth-btn" onclick="resendMFACode()">
              <span class="material-icons">info</span>
              Code Info
            </button>

            <button type="button" class="btn-secondary auth-btn" onclick="showLogin()">
              <span class="material-icons">arrow_back</span>
              Back to Login
            </button>

            <div id="mfa-feedback" class="feedback"></div>
          </form>
        </div>


      </div>


    </main>
  </div>

  <!-- SimpleWebAuthn Library -->
  <script src="https://unpkg.com/@simplewebauthn/browser/dist/bundle/index.umd.min.js"></script>

  <script type="module">
    // Supabase configuration
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase client
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Global variables
    let currentUser = null;
    let sessionTimeout = null;
    let mfaFactorId = null;
    let mfaChallengeId = null;

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      checkExistingSession();
      setupEventListeners();
      checkBiometricSupport();
    });

    // Check for existing session
    async function checkExistingSession() {
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        currentUser = session.user;
        await loadUserProfile();
        goToDashboard();
        return;
      }

      // Check if user should be remembered
      const rememberUser = localStorage.getItem('rememberUser');
      const savedEmail = localStorage.getItem('savedEmail');

      if (rememberUser === 'true' && savedEmail) {
        // Pre-fill email and check remember me
        document.getElementById('login-email').value = savedEmail;
        document.getElementById('remember-me-checkbox').checked = true;

        // Show a subtle indication that the user is remembered
        showFeedback('login-feedback', 'Welcome back! Please enter your password.', 'info');
      }
    }

    // Setup event listeners
    function setupEventListeners() {
      // Login form
      document.getElementById('login-form').addEventListener('submit', handleLogin);

      // Forgot password form
      document.getElementById('forgot-password-form').addEventListener('submit', handleForgotPassword);

      // MFA form
      document.getElementById('mfa-form').addEventListener('submit', handleMFAVerification);

      // Email input - check for biometric credentials when email is entered
      document.getElementById('login-email').addEventListener('blur', async function() {
        const email = this.value.trim();
        if (email) {
          await checkUserBiometricCredentials(email);
        } else {
          document.getElementById('biometric-login').style.display = 'none';
        }
      });

      // Biometric login
      const biometricBtn = document.getElementById('biometric-btn');
      if (biometricBtn) {
        biometricBtn.addEventListener('click', handleBiometricLogin);
      }

      // Auto-logout setup
      setupAutoLogout();
    }

    // Handle login form submission
    async function handleLogin(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const email = formData.get('email');
      const password = formData.get('password');
      const remember = formData.get('remember');

      try {
        showFeedback('login-feedback', 'Signing in...', 'info');

        const { data, error } = await supabase.auth.signInWithPassword({
          email: email,
          password: password
        });

        if (error) {
          // Check if MFA is required
          if (error.message && error.message.includes('mfa')) {
            // Store credentials temporarily for MFA
            sessionStorage.setItem('mfa_email', email);
            await initiateMFAChallenge();
            return;
          }
          throw error;
        }

        currentUser = data.user;
        await loadUserProfile();

        // Check if biometric verification was used recently
        const biometricVerified = localStorage.getItem('biometricVerified');
        const biometricEmail = localStorage.getItem('biometricVerifiedEmail');
        const biometricTime = localStorage.getItem('biometricVerifiedTime');
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);

        const recentBiometricAuth = biometricVerified === 'true' &&
                                   biometricEmail === email &&
                                   biometricTime &&
                                   parseInt(biometricTime) > fiveMinutesAgo;

        if (recentBiometricAuth) {
          // Clear biometric verification data
          localStorage.removeItem('biometricVerified');
          localStorage.removeItem('biometricVerifiedEmail');
          localStorage.removeItem('biometricVerifiedTime');

          // Skip MFA since biometric authentication was used
          showFeedback('login-feedback', 'Login successful with biometric verification! Redirecting...', 'success');
        } else {
          // Check if user has MFA factors enrolled
          const { data: factors } = await supabase.auth.mfa.listFactors();
          const verifiedFactors = factors?.totp?.filter(f => f.status === 'verified') || [];

          if (verifiedFactors.length > 0) {
            // User has MFA enabled, show challenge
            await initiateMFAChallenge();
            return;
          }
        }

        // Update last login
        await updateLastLogin();

        // Set session persistence
        if (remember) {
          localStorage.setItem('rememberUser', 'true');
          localStorage.setItem('savedEmail', email);
        } else {
          // Clear remember me data if not checked
          localStorage.removeItem('rememberUser');
          localStorage.removeItem('savedEmail');
        }

        showFeedback('login-feedback', 'Login successful! Redirecting...', 'success');

        setTimeout(() => {
          goToDashboard();
        }, 1500);

      } catch (error) {
        console.error('Login error:', error);
        showFeedback('login-feedback', error.message || 'Login failed. Please try again.', 'error');
      }
    }

    // Load user profile from staff_volunteers table
    async function loadUserProfile() {
      try {
        const { data, error } = await supabase
          .from('staff_volunteers')
          .select('*')
          .eq('user_id', currentUser.id)
          .single();

        if (error) {
          throw error;
        }

        currentUser.profile = data;
        currentUser.role = data.user_role;
        currentUser.mfa_enabled = data.mfa_enabled || false;

      } catch (error) {
        console.error('Error loading user profile:', error);
        showFeedback('login-feedback', 'Error loading user profile. Please contact an administrator.', 'error');
      }
    }

    // Handle forgot password
    async function handleForgotPassword(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const email = formData.get('email');

      try {
        showFeedback('reset-feedback', 'Sending reset link...', 'info');

        const { error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/login.html?reset=true`
        });

        if (error) {
          throw error;
        }

        showFeedback('reset-feedback', 'Password reset link sent! Check your email.', 'success');

        setTimeout(() => {
          showLogin();
        }, 3000);

      } catch (error) {
        console.error('Password reset error:', error);
        showFeedback('reset-feedback', error.message || 'Failed to send reset link.', 'error');
      }
    }

    // Initiate MFA challenge
    async function initiateMFAChallenge() {
      try {
        const { data: factors, error: factorsError } = await supabase.auth.mfa.listFactors();
        
        if (factorsError) {
          console.error('Error listing factors:', factorsError);
          throw factorsError;
        }

        console.log('Available factors:', factors);

        // Find verified TOTP factors
        const verifiedTotpFactors = factors?.totp?.filter(f => f.status === 'verified') || [];

        if (verifiedTotpFactors.length === 0) {
          throw new Error('No verified MFA factors available. Please set up MFA first.');
        }

        // Use the first verified TOTP factor
        const factor = verifiedTotpFactors[0];
        mfaFactorId = factor.id;

        // Create a challenge for this factor
        const { data: challenge, error: challengeError } = await supabase.auth.mfa.challenge({ 
          factorId: factor.id 
        });

        if (challengeError) {
          console.error('Challenge error:', challengeError);
          throw challengeError;
        }

        console.log('Challenge created:', challenge);
        mfaChallengeId = challenge.id;

        showMFAForm();
      } catch (error) {
        console.error('MFA challenge error:', error);
        showFeedback('login-feedback', error.message || 'MFA setup required. Please contact an administrator.', 'error');
      }
    }

    // Handle MFA verification
    async function handleMFAVerification(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const code = formData.get('code');

      try {
        showFeedback('mfa-feedback', 'Verifying code...', 'info');

        if (!mfaFactorId || !mfaChallengeId) {
          throw new Error('No active MFA session. Please try logging in again.');
        }

        console.log('Verifying with:', { factorId: mfaFactorId, challengeId: mfaChallengeId, code });

        const { data, error } = await supabase.auth.mfa.verify({
          factorId: mfaFactorId,
          challengeId: mfaChallengeId,
          code: code
        });

        if (error) {
          console.error('Verification error:', error);
          throw error;
        }

        console.log('MFA verification successful:', data);

        // Get the current session after MFA
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          currentUser = session.user;
          await loadUserProfile();
          await updateLastLogin();

          showFeedback('mfa-feedback', 'Verification successful! Redirecting...', 'success');

          setTimeout(() => {
            goToDashboard();
          }, 1500);
        } else {
          throw new Error('Failed to establish session after MFA');
        }

      } catch (error) {
        console.error('MFA verification error:', error);
        
        // If verification fails, we might need to create a new challenge
        if (error.message?.includes('expired') || error.message?.includes('invalid')) {
          showFeedback('mfa-feedback', 'Code expired or invalid. Generating new challenge...', 'error');
          setTimeout(() => {
            initiateMFAChallenge();
          }, 2000);
        } else {
          showFeedback('mfa-feedback', error.message || 'Invalid verification code. Please try again.', 'error');
        }
      }
    }

    // Resend MFA code
    async function resendMFACode() {
      try {
        showFeedback('mfa-feedback', 'Generating new code...', 'info');

        // For TOTP, we don't need to resend - just inform user
        showFeedback('mfa-feedback', 'Please check your authenticator app for the current code. Codes refresh every 30 seconds.', 'info');

      } catch (error) {
        showFeedback('mfa-feedback', 'Unable to refresh. Please try again.', 'error');
      }
    }

    // Update last login timestamp
    async function updateLastLogin() {
      try {
        await supabase
          .from('staff_volunteers')
          .update({
            last_login_at: new Date().toISOString(),
            last_login: new Date().toISOString() // backwards compatibility
          })
          .eq('user_id', currentUser.id);
      } catch (error) {
        console.error('Error updating last login:', error);
      }
    }

    // Auto-logout functionality
    function setupAutoLogout() {
      let timeoutId;
      const TIMEOUT_MINUTES = 10;
      const TIMEOUT_MS = TIMEOUT_MINUTES * 60 * 1000;

      function resetTimeout() {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          logout(true); // auto logout
        }, TIMEOUT_MS);
      }

      // Reset timeout on user activity
      ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, resetTimeout, true);
      });

      resetTimeout(); // Initial setup
    }

    // Logout function
    async function logout(isAutoLogout = false) {
      try {
        await supabase.auth.signOut();

        // Only clear remember me data if it's not an auto logout
        if (!isAutoLogout) {
          const shouldRemember = localStorage.getItem('rememberUser') === 'true';
          if (!shouldRemember) {
            localStorage.removeItem('rememberUser');
            localStorage.removeItem('savedEmail');
          }
        }

        if (isAutoLogout) {
          alert('You have been automatically logged out due to inactivity.');
        }

        // Redirect to login
        window.location.reload();

      } catch (error) {
        console.error('Logout error:', error);
      }
    }

    // UI Helper Functions
    function showLogin() {
      document.getElementById('login-form-container').style.display = 'block';
      document.getElementById('forgot-password-container').style.display = 'none';
      document.getElementById('mfa-container').style.display = 'none';
    }

    function showForgotPassword() {
      document.getElementById('login-form-container').style.display = 'none';
      document.getElementById('forgot-password-container').style.display = 'block';
    }

    function showMFAForm() {
      document.getElementById('login-form-container').style.display = 'none';
      document.getElementById('mfa-container').style.display = 'block';
      // Focus on the MFA code input
      setTimeout(() => {
        document.getElementById('mfa-code').focus();
      }, 100);
    }

    function goToDashboard() {
      window.location.href = 'dashboard.html';
    }

    function togglePasswordVisibility(inputId) {
      const input = document.getElementById(inputId);
      const icon = input.nextElementSibling.querySelector('.material-icons');

      if (input.type === 'password') {
        input.type = 'text';
        icon.textContent = 'visibility_off';
      } else {
        input.type = 'password';
        icon.textContent = 'visibility';
      }
    }

    function showFeedback(elementId, message, type) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="feedback ${type}">${message}</div>`;
    }

    // Biometric authentication support
    async function checkBiometricSupport() {
      try {
        if (!window.PublicKeyCredential) {
          return;
        }

        const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
        if (!available) {
          return;
        }

        // Check if any user has biometric credentials (we'll check specific user after email is entered)
        const savedEmail = localStorage.getItem('savedEmail');
        if (savedEmail) {
          await checkUserBiometricCredentials(savedEmail);
        }
      } catch (error) {
        console.error('Error checking biometric support:', error);
      }
    }

    // Check if specific user has biometric credentials
    async function checkUserBiometricCredentials(email) {
      try {
        // First get the user by email
        const { data: users } = await supabase
          .from('staff_volunteers')
          .select('user_id')
          .eq('email', email)
          .eq('biometric_enabled', true);

        if (users && users.length > 0) {
          const { data: credentials } = await supabase
            .from('webauthn_credentials')
            .select('*')
            .eq('user_id', users[0].user_id)
            .eq('is_active', true);

          if (credentials && credentials.length > 0) {
            document.getElementById('biometric-login').style.display = 'block';
          }
        }
      } catch (error) {
        console.error('Error checking user biometric credentials:', error);
      }
    }

    async function handleBiometricLogin() {
      try {
        showFeedback('login-feedback', 'Preparing biometric authentication...', 'info');

        // Get email from form
        const email = document.getElementById('login-email').value.trim();
        if (!email) {
          throw new Error('Please enter your email first');
        }

        // Get user by email
        const { data: users } = await supabase
          .from('staff_volunteers')
          .select('user_id, email')
          .eq('email', email)
          .eq('biometric_enabled', true);

        if (!users || users.length === 0) {
          throw new Error('No biometric authentication found for this email');
        }

        const { data: credentials } = await supabase
          .from('webauthn_credentials')
          .select('*')
          .eq('user_id', users[0].user_id)
          .eq('is_active', true);

        if (!credentials || credentials.length === 0) {
          throw new Error('No biometric credentials found. Please set up biometric authentication first.');
        }

        // Create authentication options
        const authenticationOptions = {
          publicKey: {
            challenge: new Uint8Array(32),
            allowCredentials: credentials.map(cred => ({
              id: Uint8Array.from(atob(cred.credential_id), c => c.charCodeAt(0)),
              type: 'public-key',
              transports: ['internal'] // Hint for platform authenticators like Touch ID
            })),
            userVerification: 'required',
            timeout: 60000,
            rpId: (location.hostname === 'localhost' || location.hostname === '127.0.0.1') ? 'localhost' : location.hostname
          }
        };

        // Fill challenge with random data
        crypto.getRandomValues(authenticationOptions.publicKey.challenge);

        showFeedback('login-feedback', 'Please use Touch ID or Face ID to authenticate...', 'info');

        // Use native WebAuthn API for authentication
        const authenticationResponse = await navigator.credentials.get(authenticationOptions);

        // Verify the authentication
        const verification = await verifyBiometricAuthentication(authenticationResponse, credentials);

        if (verification.verified) {
          // Update last used timestamp
          await supabase
            .from('webauthn_credentials')
            .update({
              last_used_at: new Date().toISOString(),
              counter: verification.counter
            })
            .eq('credential_id', btoa(String.fromCharCode(...new Uint8Array(authenticationResponse.rawId))));

          // Store biometric verification status
          localStorage.setItem('biometricVerified', 'true');
          localStorage.setItem('biometricVerifiedEmail', email);
          localStorage.setItem('biometricVerifiedTime', Date.now().toString());

          showFeedback('login-feedback', 'Biometric verification successful! Please enter your password to complete login.', 'success');

          // Pre-fill email and focus password field
          document.getElementById('login-email').value = email;
          document.getElementById('login-password').focus();

          // Show a visual indicator that biometric verification was successful
          document.getElementById('login-password').placeholder = 'Password (biometric verified ✓)';
          return;
        } else {
          throw new Error('Biometric verification failed');
        }

      } catch (error) {
        console.error('Biometric authentication error:', error);
        showFeedback('login-feedback', error.message || 'Biometric authentication failed. Please try password login.', 'error');
      }
    }

    // Verify biometric authentication (simplified client-side verification)
    async function verifyBiometricAuthentication(authResponse, credentials) {
      try {
        // Find the credential that was used
        const credentialId = btoa(String.fromCharCode(...new Uint8Array(authResponse.rawId)));
        const credential = credentials.find(cred => cred.credential_id === credentialId);

        if (!credential) {
          return { verified: false };
        }

        // In a production environment, you would send this to your server for proper verification
        // For now, we'll do basic client-side verification
        const clientDataJSON = JSON.parse(new TextDecoder().decode(authResponse.response.clientDataJSON));

        // Basic checks
        if (clientDataJSON.type !== 'webauthn.get') {
          return { verified: false };
        }

        // Update counter (in production, this should be done server-side)
        const newCounter = (credential.counter || 0) + 1;

        return {
          verified: true,
          counter: newCounter
        };
      } catch (error) {
        console.error('Verification error:', error);
        return { verified: false };
      }
    }

    // Make functions globally available
    window.showLogin = showLogin;
    window.showForgotPassword = showForgotPassword;
    window.goToDashboard = goToDashboard;
    window.logout = logout;
    window.togglePasswordVisibility = togglePasswordVisibility;
    window.resendMFACode = resendMFACode;

  </script>
</