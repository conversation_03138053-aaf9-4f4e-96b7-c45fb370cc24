-- Add Gender Column to Animals Table
-- Run this in Supabase SQL Editor to add the gender column

-- Add gender column to animals table
ALTER TABLE animals 
ADD COLUMN gender TEXT;

-- Add comment for documentation
COMMENT ON COLUMN animals.gender IS 'Gender of the animal (Male, Female, Unknown)';

-- Optional: Add a check constraint for valid gender values
-- Uncomment the line below if you want to restrict gender values
-- ALTER TABLE animals ADD CONSTRAINT animals_gender_check CHECK (gender IN ('Male', 'Female', 'Unknown') OR gender IS NULL);

-- Create index for better performance (optional)
CREATE INDEX IF NOT EXISTS idx_animals_gender ON animals(gender);

-- Sample update to set gender for existing records (optional)
-- UPDATE animals SET gender = 'Unknown' WHERE gender IS NULL;
