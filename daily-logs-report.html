<!DOCTYPE html>
<html lang="en">
<head>
 <!-- REPLACE the existing viewport meta tag in ALL your HTML files with this enhanced version -->

<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
<meta name="theme-color" content="#4285a6" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
<meta name="mobile-web-app-capable" content="yes" />

<!-- Additional meta tags for better mobile support -->
<meta name="format-detection" content="telephone=no" />
<meta name="msapplication-tap-highlight" content="no" />
<meta name="apple-touch-fullscreen" content="yes" />

<!-- Prevent iOS from changing colors -->
<meta name="apple-mobile-web-app-title" content="Cornish Birds of Prey" />
<meta name="application-name" content="Cornish Birds of Prey" />

<!-- Ensure proper rendering on Windows Phone -->
<meta name="msapplication-TileColor" content="#4285a6" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <title>Daily Logs Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    /* Report-specific styles */
    .report-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      background: white;
      min-height: 100vh;
    }

    .report-header {
      text-align: center;
      margin-bottom: 2rem;
      border-bottom: 2px solid #2c3e50;
      padding-bottom: 1rem;
    }

    .report-title {
      color: #2c3e50;
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .report-subtitle {
      color: #666;
      font-size: 1.1rem;
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1.5rem;
      text-align: center;
      border-left: 4px solid #4285a4;
    }

    .summary-number {
      font-size: 2rem;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }

    .summary-label {
      color: #666;
      font-size: 0.9rem;
    }

    .logs-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    .logs-table th, .logs-table td {
      border: 1px solid #ddd;
      padding: 0.75rem;
      text-align: left;
      vertical-align: top;
    }

    .logs-table th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
      position: sticky;
      top: 0;
    }

    .logs-table tr:nth-child(even) {
      background: #f9f9f9;
    }

    .animal-name {
      font-weight: 600;
      color: #2c3e50;
    }

    .status-fed {
      color: #28a745;
      font-weight: 500;
    }

    .status-not-fed {
      color: #dc3545;
      font-weight: 500;
    }

    .status-cleaned {
      color: #28a745;
      font-weight: 500;
    }

    .status-not-cleaned {
      color: #dc3545;
      font-weight: 500;
    }

    .floating-buttons {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 1000;
    }

    .floating-btn {
      background: rgba(66, 133, 166, 0.9);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .floating-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .close-btn {
      background: rgba(66, 133, 166, 0.9);
    }

    .close-btn:hover {
      background: rgba(66, 133, 166, 1);
      transform: translateY(-2px);
    }

    .no-data {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 2rem;
    }

    .loading {
      text-align: center;
      color: #666;
      padding: 2rem;
    }

    /* Print styles */
    @media print {
      .floating-buttons {
        display: none !important;
      }
      
      .report-container {
        padding: 0;
        box-shadow: none;
      }
      
      .summary-section {
        page-break-inside: avoid;
      }
      
      .logs-table {
        page-break-inside: auto;
      }
      
      .logs-table tr {
        page-break-inside: avoid;
      }
    }

    /* Responsive Design */

    /* Mobile (320px - 768px) */
    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      .report-container {
        padding: 1rem;
        margin: 0;
      }

      .report-title {
        font-size: 1.6rem;
      }

      .report-subtitle {
        font-size: 1rem;
      }

      .summary-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
      }

      .summary-card {
        padding: 1rem;
      }

      .summary-number {
        font-size: 1.6rem;
      }

      .summary-label {
        font-size: 0.8rem;
      }

      .floating-buttons {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-bottom: 1rem;
        gap: 8px;
      }

      .floating-btn {
        padding: 10px 15px;
        font-size: 0.8rem;
      }

      .logs-table {
        font-size: 0.8rem;
        display: block;
        overflow-x: auto;
        white-space: nowrap;
      }

      .logs-table th, .logs-table td {
        padding: 0.5rem 0.3rem;
        min-width: 80px;
      }

      .animal-name {
        min-width: 100px;
      }
    }

    /* Tablet Portrait (768px - 1024px) */
    @media (min-width: 768px) and (max-width: 1024px) {
      .report-container {
        padding: 1.5rem;
      }

      .report-title {
        font-size: 1.8rem;
      }

      .summary-section {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
      }

      .summary-card {
        padding: 1.2rem;
      }

      .summary-number {
        font-size: 1.8rem;
      }

      .floating-buttons {
        top: 15px;
        right: 15px;
      }

      .floating-btn {
        padding: 11px 18px;
        font-size: 0.85rem;
      }

      .logs-table {
        font-size: 0.9rem;
      }

      .logs-table th, .logs-table td {
        padding: 0.6rem;
      }
    }

    /* Tablet Landscape (1024px - 1200px) */
    @media (min-width: 1024px) and (max-width: 1200px) {
      .report-container {
        padding: 1.8rem;
      }

      .summary-section {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      }
    }

    /* Small Mobile (320px - 480px) */
    @media (max-width: 480px) {
      body {
        padding: 0.5rem;
      }

      .report-container {
        padding: 0.8rem;
      }

      .report-title {
        font-size: 1.4rem;
      }

      .report-subtitle {
        font-size: 0.9rem;
      }

      .summary-section {
        grid-template-columns: 1fr;
        gap: 0.6rem;
      }

      .summary-card {
        padding: 0.8rem;
      }

      .summary-number {
        font-size: 1.4rem;
      }

      .summary-label {
        font-size: 0.75rem;
      }

      .floating-buttons {
        flex-direction: column;
        gap: 5px;
      }

      .floating-btn {
        padding: 8px 12px;
        font-size: 0.75rem;
      }

      .logs-table {
        font-size: 0.75rem;
      }

      .logs-table th, .logs-table td {
        padding: 0.4rem 0.2rem;
        min-width: 70px;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Action Buttons -->
  <div class="floating-buttons">
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="floating-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
    <button class="floating-btn close-btn" onclick="closeWindow()">
      <span class="material-icons">close</span>
      Close Window
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Daily Care Logs Report</h1>
      <p class="report-subtitle" id="report-subtitle">Loading...</p>
    </div>

    <div class="summary-section" id="summary-section">
      <div class="summary-card">
        <div class="summary-number" id="total-logs">-</div>
        <div class="summary-label">Total Log Entries</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="animals-logged">-</div>
        <div class="summary-label">Animals Logged</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="feeding-completion">-</div>
        <div class="summary-label">Feeding Completion</div>
      </div>
      <div class="summary-card">
        <div class="summary-number" id="cleaning-completion">-</div>
        <div class="summary-label">Cleaning Completion</div>
      </div>
    </div>

    <div id="logs-content">
      <div class="loading">Loading daily logs data...</div>
    </div>
  </div>

  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const supabase = createClient(
      'https://wkclogfpyykwgjhhshsi.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k'
    );

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const options = urlParams.get('options')?.split(',') || ['feeding', 'cleaning', 'staff-attribution', 'notes'];
    const startDate = urlParams.get('startDate');
    const endDate = urlParams.get('endDate');

    // Initialize report
    async function initializeReport() {
      if (!startDate || !endDate) {
        document.getElementById('logs-content').innerHTML = '<div class="no-data">Invalid date range provided.</div>';
        return;
      }

      // Update report subtitle
      document.getElementById('report-subtitle').textContent = `${startDate} to ${endDate}`;

      // Load daily logs data
      await loadDailyLogsData();
    }

    // Load daily logs data
    async function loadDailyLogsData() {
      const { data, error } = await supabase
        .from('daily_logs')
        .select(`
          *,
          animals (
            name,
            species,
            Group
          )
        `)
        .gte('created_on', startDate)
        .lte('created_on', endDate)
        .order('created_on', { ascending: false });

      if (error) {
        console.error('Error loading daily logs:', error);
        document.getElementById('logs-content').innerHTML = '<div class="no-data">Error loading daily logs data.</div>';
        return;
      }

      if (!data || data.length === 0) {
        document.getElementById('logs-content').innerHTML = '<div class="no-data">No daily logs found for this period.</div>';
        return;
      }

      // Calculate summary statistics
      calculateSummaryStats(data);

      // Generate table
      generateLogsTable(data);
    }

    // Calculate summary statistics
    function calculateSummaryStats(data) {
      const totalLogs = data.length;
      const uniqueAnimals = new Set(data.map(log => log.animal_id)).size;
      
      const fedCount = data.filter(log => log.fed === 'Yes' || log.fed === 'Fed').length;
      const cleanedCount = data.filter(log => log.cleaned === 'Yes' || log.cleaned === 'Cleaned').length;
      
      const feedingCompletion = totalLogs > 0 ? Math.round((fedCount / totalLogs) * 100) : 0;
      const cleaningCompletion = totalLogs > 0 ? Math.round((cleanedCount / totalLogs) * 100) : 0;

      document.getElementById('total-logs').textContent = totalLogs;
      document.getElementById('animals-logged').textContent = uniqueAnimals;
      document.getElementById('feeding-completion').textContent = `${feedingCompletion}%`;
      document.getElementById('cleaning-completion').textContent = `${cleaningCompletion}%`;
    }

    // Generate logs table
    function generateLogsTable(data) {
      let tableHTML = '<table class="logs-table"><thead><tr>';
      
      // Add headers based on selected options
      tableHTML += '<th>Date</th>';
      tableHTML += '<th>Animal</th>';
      
      if (options.includes('feeding')) {
        tableHTML += '<th>Fed</th>';
      }

      if (options.includes('cleaning')) {
        tableHTML += '<th>Cleaned</th>';
      }
      
      if (options.includes('notes')) {
        tableHTML += '<th>Notes</th>';
      }
      
      if (options.includes('staff-attribution')) {
        tableHTML += '<th>Staff Member</th>';
      }
      
      tableHTML += '</tr></thead><tbody>';

      // Add data rows
      data.forEach(log => {
        tableHTML += '<tr>';
        tableHTML += `<td>${new Date(log.created_on).toLocaleDateString()}</td>`;
        tableHTML += `<td class="animal-name">${log.animals?.name || 'Unknown Animal'}</td>`;
        
        if (options.includes('feeding')) {
          const feedingClass = (log.fed === 'Yes' || log.fed === 'Fed') ? 'status-fed' : 'status-not-fed';
          tableHTML += `<td class="${feedingClass}">${log.fed || 'Not Recorded'}</td>`;
        }

        if (options.includes('cleaning')) {
          const cleaningClass = (log.cleaned === 'Yes' || log.cleaned === 'Cleaned') ? 'status-cleaned' : 'status-not-cleaned';
          tableHTML += `<td class="${cleaningClass}">${log.cleaned || 'Not Recorded'}</td>`;
        }
        
        if (options.includes('notes')) {
          tableHTML += `<td>${log.notes || 'No notes'}</td>`;
        }
        
        if (options.includes('staff-attribution')) {
          const staffName = log.logged_by || 'Unknown Staff';
          tableHTML += `<td>${staffName}</td>`;
        }
        
        tableHTML += '</tr>';
      });

      tableHTML += '</tbody></table>';
      document.getElementById('logs-content').innerHTML = tableHTML;
    }

    // Initialize the report
    initializeReport();

    // Close window function - make it globally accessible
    function closeWindow() {
      window.location.href = 'reports.html';
    }

    // Make closeWindow globally accessible
    window.closeWindow = closeWindow;
  </script>
</body>
</html>
