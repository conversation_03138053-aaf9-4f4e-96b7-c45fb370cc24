# Supabase Auth Configuration Guide
## Cornish Birds of Prey Center Authentication Setup

### 1. Password Policy Configuration

In your Supabase Dashboard, go to **Authentication > Settings** and configure:

```json
{
  "password_requirements": {
    "min_length": 12,
    "require_uppercase": true,
    "require_lowercase": true,
    "require_numbers": true,
    "require_special_characters": true,
    "special_characters": "!@#$%^&*()_+-=[]{}|;:,.<>?"
  }
}
```

### 2. Email Templates Configuration

#### Password Reset Email Template
```html
<h2>Reset Your Password</h2>
<p>Hello,</p>
<p>You have requested to reset your password for your Cornish Birds of Prey Center account.</p>
<p>Click the link below to reset your password:</p>
<p><a href="{{ .ConfirmationURL }}">Reset Password</a></p>
<p>This link will expire in 24 hours.</p>
<p>If you did not request this password reset, please ignore this email.</p>
<p>Best regards,<br>Cornish Birds of Prey Center</p>
```

#### Email Confirmation Template
```html
<h2>Confirm Your Email</h2>
<p>Hello {{ .Email }},</p>
<p>Welcome to the Cornish Birds of Prey Center management system!</p>
<p>Please click the link below to confirm your email address:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm Email</a></p>
<p>Best regards,<br>Cornish Birds of Prey Center</p>
```

### 3. Auth Settings Configuration

In **Authentication > Settings**, set:

- **Site URL**: `https://your-domain.com` (or `http://localhost:3000` for development)
- **Redirect URLs**: 
  - `https://your-domain.com/dashboard`
  - `http://localhost:3000/dashboard` (for development)
- **JWT Expiry**: `3600` (1 hour)
- **Refresh Token Expiry**: `604800` (7 days)
- **Enable email confirmations**: `true`
- **Enable phone confirmations**: `false` (we'll use SMS for 2FA only)

### 4. Multi-Factor Authentication (MFA) Setup

Enable **Phone (SMS) MFA**:
- Go to **Authentication > Settings > Multi-Factor Authentication**
- Enable **Phone (SMS)**
- Configure your SMS provider (Twilio recommended)

### 5. Rate Limiting Configuration

Set these limits to prevent abuse:
- **Email sending rate**: 60 per hour
- **SMS sending rate**: 10 per hour  
- **Password reset attempts**: 5 per hour
- **Login attempts**: 10 per 5 minutes

### 6. Session Configuration

- **Session timeout**: 600 seconds (10 minutes)
- **Refresh token rotation**: Enabled
- **Reuse interval**: 10 seconds

### 7. Security Settings

- **Enable Captcha**: Recommended for production
- **Enable email rate limiting**: Yes
- **Enable phone rate limiting**: Yes
- **JWT Secret**: Auto-generated (don't change)

### 8. Custom Claims Setup

You'll need to create a Database Function for custom claims:

```sql
CREATE OR REPLACE FUNCTION auth.get_user_role(user_id UUID)
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT user_role 
    FROM staff_volunteers 
    WHERE staff_volunteers.user_id = $1 
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 9. Webhook Configuration (Optional)

For advanced user management, set up webhooks:
- **Webhook URL**: `https://your-domain.com/api/auth/webhook`
- **Events**: `user.created`, `user.updated`, `user.deleted`

### 10. Environment Variables

Add these to your application:

```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 11. Initial Admin User Setup

After running the database schema:

1. **Create Admin User in Supabase Auth**:
   - Go to Authentication > Users
   - Click "Add User"
   - Enter your email and temporary password
   - Set email as confirmed

2. **Link to Staff Record**:
   ```sql
   -- Replace with your actual user ID and staff record ID
   UPDATE staff_volunteers 
   SET user_id = 'your-auth-user-id', 
       user_role = 'Admin',
       is_active = true
   WHERE email = '<EMAIL>';
   ```

### 12. Testing Checklist

- [ ] Password policy enforced (12+ chars, mixed case, numbers, symbols)
- [ ] Email confirmation working
- [ ] Password reset working
- [ ] SMS 2FA working (if enabled)
- [ ] Session timeout at 10 minutes
- [ ] Role-based access working
- [ ] Audit logging working

### 13. Production Security Checklist

- [ ] Enable Captcha
- [ ] Set up proper CORS policies
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerts
- [ ] Regular security audits
- [ ] Backup authentication data

### 14. Troubleshooting

**Common Issues**:
- Email not sending: Check SMTP configuration
- SMS not working: Verify Twilio credentials
- Session timeout not working: Check JWT expiry settings
- Role access issues: Verify RLS policies

**Debug Mode**:
Enable debug logging in development:
```javascript
const { createClient } = require('@supabase/supabase-js')
const supabase = createClient(url, key, {
  auth: {
    debug: true
  }
})
```

This configuration provides enterprise-level security while maintaining usability for your staff and volunteers.
