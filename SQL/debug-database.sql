-- Debug script to check database issues
-- Run this in Supabase SQL Editor to diagnose problems

-- 1. Check if animals table exists and its structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'animals' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check RLS policies on animals table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'animals';

-- 3. Check if RLS is enabled on animals table
SELECT schemaname, tablename, rowsecurity, forcerowsecurity
FROM pg_tables 
WHERE tablename = 'animals';

-- 4. Test a simple insert (replace with actual user ID)
-- INSERT INTO animals (name, species, status, "Group", notes) 
-- VALUES ('Test Bird', 'Test Species', 'All Clear', 'Owls', 'Test notes');

-- 5. Check status enum values (if it's an enum)
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'status_enum' OR typname LIKE '%status%'
);

-- 6. Check if there are any triggers that might be failing
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers
WHERE event_object_table = 'animals';

-- 7. Check current user and their permissions
SELECT current_user, session_user;

-- 8. Check if storage bucket exists
-- This won't work in SQL editor, but you can check in Supabase dashboard
-- Storage > Buckets > look for 'animal-photos'
