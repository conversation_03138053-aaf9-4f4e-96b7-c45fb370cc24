-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.adoptions_donations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  animal_id uuid,
  donor_name text NOT NULL,
  donor_email text,
  donation_type USER-DEFINED NOT NULL,
  amount numeric NOT NULL,
  start_date date NOT NULL,
  end_date date NOT NULL,
  notes text,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  created_by uuid,
  updated_by uuid,
  status text,
  CONSTRAINT adoptions_donations_pkey PRIMARY KEY (id),
  CONSTRAINT adoptions_donations_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT adoptions_donations_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT adoptions_donations_animal_id_fkey FOREIGN KEY (animal_id) REFERENCES public.animals(id)
);
CREATE TABLE public.animals (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text,
  species text NOT NULL,
  status USER-DEFINED NOT NULL,
  photo_url text,
  notes text,
  created_on timestamp with time zone NOT NULL DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  scientific_name text,
  Age text,
  Group text,
  gender USER-DEFINED,
  is_archived boolean NOT NULL DEFAULT false,
  archived_on timestamp with time zone,
  date_of_death date,
  reason_for_death text,
  CONSTRAINT animals_pkey PRIMARY KEY (id),
  CONSTRAINT animals_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT animals_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.audit_log (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  action text NOT NULL,
  table_name text NOT NULL,
  record_id uuid,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT audit_log_pkey PRIMARY KEY (id),
  CONSTRAINT audit_log_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.daily_logs (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_on timestamp with time zone NOT NULL DEFAULT now(),
  animal_id uuid NOT NULL,
  fed text,
  cleaned text,
  medication text,
  notes text,
  created_by uuid,
  updated_by uuid,
  logged_by text,
  CONSTRAINT daily_logs_pkey PRIMARY KEY (id),
  CONSTRAINT daily_logs_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT daily_logs_animal_id_fkey FOREIGN KEY (animal_id) REFERENCES public.animals(id),
  CONSTRAINT daily_logs_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.documents (
  id bigint NOT NULL DEFAULT nextval('documents_id_seq'::regclass),
  filename text NOT NULL,
  original_filename text NOT NULL,
  file_path text NOT NULL UNIQUE,
  file_url text NOT NULL,
  category text NOT NULL CHECK (category = ANY (ARRAY['policy'::text, 'risk-assessment'::text, 'emergency'::text, 'medical'::text, 'other'::text])),
  file_size bigint NOT NULL,
  mime_type text,
  uploaded_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone,
  updated_at timestamp with time zone DEFAULT now(),
  user_id uuid,
  review_date date,
  CONSTRAINT documents_pkey PRIMARY KEY (id),
  CONSTRAINT documents_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.emergency_contacts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  contact_name text NOT NULL,
  role text NOT NULL,
  phone text NOT NULL,
  alt_phone text,
  email text,
  notes text,
  priority_level USER-DEFINED NOT NULL DEFAULT 'medium'::priority_level_enum,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT emergency_contacts_pkey PRIMARY KEY (id),
  CONSTRAINT emergency_contacts_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT emergency_contacts_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.medical_episodes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  animal_id uuid NOT NULL,
  title character varying NOT NULL,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['injury'::character varying, 'illness'::character varying, 'surgery'::character varying, 'rehabilitation'::character varying, 'preventive'::character varying, 'other'::character varying]::text[])),
  severity character varying NOT NULL CHECK (severity::text = ANY (ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'critical'::character varying]::text[])),
  description text NOT NULL,
  date_discovered date NOT NULL,
  status character varying NOT NULL DEFAULT 'active'::character varying CHECK (status::text = ANY (ARRAY['active'::character varying::text, 'monitoring'::character varying::text, 'resolved'::character varying::text, 'deceased'::character varying::text])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT medical_episodes_pkey PRIMARY KEY (id),
  CONSTRAINT medical_episodes_animal_id_fkey FOREIGN KEY (animal_id) REFERENCES public.animals(id),
  CONSTRAINT medical_episodes_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT medical_episodes_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.medical_interventions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  episode_id uuid NOT NULL,
  intervention_date timestamp with time zone NOT NULL,
  intervention_type character varying NOT NULL CHECK (intervention_type::text = ANY (ARRAY['examination'::character varying::text, 'medication'::character varying::text, 'treatment'::character varying::text, 'surgery'::character varying::text, 'therapy'::character varying::text, 'monitoring'::character varying::text, 'euthanasia'::character varying::text, 'other'::character varying::text])),
  description text NOT NULL,
  staff_member character varying,
  outcome text,
  next_steps text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT medical_interventions_pkey PRIMARY KEY (id),
  CONSTRAINT medical_interventions_episode_id_fkey FOREIGN KEY (episode_id) REFERENCES public.medical_episodes(id),
  CONSTRAINT medical_interventions_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT medical_interventions_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);
CREATE TABLE public.mfa_backup_codes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  code_hash text NOT NULL,
  used_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT mfa_backup_codes_pkey PRIMARY KEY (id),
  CONSTRAINT mfa_backup_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.species_list (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  enum_key text NOT NULL,
  common_name text NOT NULL,
  scientific_name text NOT NULL,
  taxonomic_group text NOT NULL,
  family text NOT NULL,
  native_range text NOT NULL,
  cites_status text NOT NULL,
  uk_native boolean NOT NULL,
  CONSTRAINT species_list_pkey PRIMARY KEY (id)
);
CREATE TABLE public.staff_volunteers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  role text NOT NULL CHECK (role = ANY (ARRAY['Staff'::text, 'Volunteer'::text])),
  position text NOT NULL,
  email text NOT NULL UNIQUE,
  phone_number text,
  start_date date NOT NULL,
  status text NOT NULL DEFAULT 'Active'::text CHECK (status = ANY (ARRAY['Active'::text, 'Inactive'::text])),
  photo_url text,
  last_login timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id uuid,
  user_role text NOT NULL DEFAULT 'Volunteer'::text CHECK (user_role = ANY (ARRAY['Admin'::text, 'Staff'::text, 'Volunteer'::text])),
  is_active boolean NOT NULL DEFAULT true,
  last_login_at timestamp with time zone,
  password_reset_required boolean NOT NULL DEFAULT false,
  created_by uuid,
  updated_by uuid,
  mfa_enabled boolean DEFAULT false,
  mfa_secret text,
  mfa_backup_codes ARRAY,
  biometric_enabled boolean DEFAULT false,
  mfa_enrolled_at timestamp with time zone,
  last_mfa_verification timestamp with time zone,
  CONSTRAINT staff_volunteers_pkey PRIMARY KEY (id),
  CONSTRAINT staff_volunteers_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id),
  CONSTRAINT staff_volunteers_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT staff_volunteers_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_preferences (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE,
  theme text DEFAULT 'default'::text,
  language text DEFAULT 'en'::text,
  timezone text DEFAULT 'Europe/London'::text,
  notifications_enabled boolean DEFAULT true,
  two_factor_enabled boolean DEFAULT false,
  biometric_enabled boolean DEFAULT false,
  auto_logout_minutes integer DEFAULT 10,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  mfa_method text DEFAULT 'none'::text CHECK (mfa_method = ANY (ARRAY['none'::text, 'totp'::text, 'sms'::text, 'both'::text])),
  CONSTRAINT user_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_sessions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  session_token text NOT NULL UNIQUE,
  device_info jsonb,
  ip_address inet,
  last_activity timestamp with time zone NOT NULL DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.webauthn_credentials (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  credential_id text NOT NULL UNIQUE,
  public_key text NOT NULL,
  counter bigint NOT NULL DEFAULT 0,
  device_type text NOT NULL DEFAULT 'unknown'::text,
  device_name text,
  created_at timestamp with time zone DEFAULT now(),
  last_used_at timestamp with time zone,
  is_active boolean DEFAULT true,
  CONSTRAINT webauthn_credentials_pkey PRIMARY KEY (id),
  CONSTRAINT webauthn_credentials_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);