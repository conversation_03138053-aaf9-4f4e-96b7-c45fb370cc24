<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>MFA Setup – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <!-- Header -->
    <header class="top-bar">
      <div class="top-bar-content">
        <div class="top-bar-left">
          <button class="back-btn" onclick="goToDashboard()">
            <span class="material-icons">arrow_back</span>
            Dashboard
          </button>
        </div>
        <h1>Multi-Factor Authentication Setup</h1>
        <div id="user-display" class="user-display"></div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="mfa-setup-container">
        
        <!-- MFA Status Card -->
        <div class="card">
          <div class="card-header">
            <h2>
              <span class="material-icons">security</span>
              Security Status
            </h2>
          </div>
          <div class="card-content">
            <div id="mfa-status" class="mfa-status">
              <div class="status-item">
                <span class="material-icons">smartphone</span>
                <div class="status-info">
                  <h3>Authenticator App (TOTP)</h3>
                  <p id="totp-status">Not configured</p>
                </div>
                <button id="totp-action-btn" class="btn-primary" onclick="setupTOTP()">
                  <span class="material-icons">add</span>
                  Setup
                </button>
              </div>

              <div class="status-item">
                <span class="material-icons">fingerprint</span>
                <div class="status-info">
                  <h3>Biometric Authentication</h3>
                  <p id="biometric-status">Not configured</p>
                </div>
                <button id="biometric-action-btn" class="btn-primary" onclick="setupBiometric()">
                  <span class="material-icons">add</span>
                  Setup
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Test Output -->
        <div class="card">
          <div class="card-header">
            <h2>Test Output</h2>
          </div>
          <div class="card-content">
            <div id="test-output">
              <p>Click the buttons above to test functionality.</p>
            </div>
          </div>
        </div>

      </div>
    </main>

    <!-- Feedback Modal -->
    <div id="feedback-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <div id="feedback-content"></div>
      </div>
    </div>
  </div>

  <script>
    console.log('Script started');
    
    // Test functions
    function setupTOTP() {
      console.log('setupTOTP function called');
      document.getElementById('test-output').innerHTML = '<p style="color: green;">✓ setupTOTP function works!</p>';
      showFeedback('TOTP setup clicked!', 'success');
    }
    
    function setupBiometric() {
      console.log('setupBiometric function called');
      document.getElementById('test-output').innerHTML = '<p style="color: green;">✓ setupBiometric function works!</p>';
      showFeedback('Biometric setup clicked!', 'success');
    }
    
    function goToDashboard() {
      console.log('goToDashboard function called');
      document.getElementById('test-output').innerHTML = '<p style="color: blue;">✓ goToDashboard function works!</p>';
      showFeedback('Dashboard navigation clicked!', 'info');
    }
    
    function showFeedback(message, type) {
      console.log('showFeedback called:', message, type);
      const modal = document.getElementById('feedback-modal');
      const content = document.getElementById('feedback-content');
      content.innerHTML = `<div class="feedback ${type}">${message}</div>`;
      modal.style.display = 'block';
      
      setTimeout(() => {
        modal.style.display = 'none';
      }, 3000);
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Page loaded successfully');
      document.getElementById('test-output').innerHTML = '<p style="color: blue;">✓ Page loaded and JavaScript is working!</p>';
      showFeedback('Page loaded successfully!', 'success');
    });
    
    console.log('Script ended');
  </script>
</body>
</html>
