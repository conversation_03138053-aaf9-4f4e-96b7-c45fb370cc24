<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Medical Welfare Tracker</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <span class="title">Medical Welfare Tracker</span>
      <a href="#" id="back-to-animal" class="back-link">←</a>
    </header>

    <main class="medical-tracker">
    <!-- Animal Summary -->
    <section class="animal-summary">
      <div class="animal-header">
        <img id="animal-photo" src="assets/images/placeholder.jpg" alt="Animal Photo" class="animal-photo-small">
        <div class="animal-basic-info">
          <h1 id="animal-name">Loading...</h1>
          <p><strong>Species:</strong> <span id="animal-species">—</span></p>
          <p><strong>Status:</strong> <span id="animal-status">—</span></p>
        </div>
      </div>
    </section>

    <!-- Deceased Animal Alert (hidden by default) -->
    <section id="deceased-alert" class="deceased-alert" style="display: none;">
      <div class="alert-content">
        <span class="material-icons">warning</span>
        <div class="alert-text">
          <strong>⚠️ This animal was marked deceased on <span id="death-date"></span>.</strong>
          <br>
          <span>Reason: <span id="death-reason"></span></span>
        </div>
      </div>
    </section>

    <!-- Medical Episodes Overview -->
    <section class="medical-overview">
      <div class="section-header">
        <h2>Medical Episodes</h2>
        <button class="btn-primary" onclick="openNewEpisodeModal()">
          <span class="material-icons">add</span>
          New Episode
        </button>
      </div>
      <div id="episodes-container" class="episodes-container">
        <!-- Episodes will be loaded here -->
      </div>
    </section>
  </main>
  </div>

  <!-- New Episode Modal -->
  <div id="new-episode-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeNewEpisodeModal()">✖</span>
      <h2>New Medical Episode</h2>
      <form id="new-episode-form">
        <div class="form-group">
          <label for="episode-title">Episode Title *</label>
          <input type="text" id="episode-title" name="title" required placeholder="e.g., Broken Wing, Eye Infection">
        </div>
        
        <div class="form-group">
          <label for="episode-type">Episode Type *</label>
          <select id="episode-type" name="type" required>
            <option value="">Select type...</option>
            <option value="injury">Injury</option>
            <option value="illness">Illness</option>
            <option value="surgery">Surgery</option>
            <option value="rehabilitation">Rehabilitation</option>
            <option value="preventive">Preventive Care</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="episode-severity">Severity *</label>
          <select id="episode-severity" name="severity" required>
            <option value="">Select severity...</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="episode-description">Initial Description *</label>
          <textarea id="episode-description" name="description" required rows="4" placeholder="Describe the initial condition, symptoms, or circumstances..."></textarea>
        </div>
        
        <div class="form-group">
          <label for="episode-date">Date Discovered *</label>
          <input type="date" id="episode-date" name="date_discovered" required>
        </div>
        
        <div class="form-group">
          <label for="episode-status">Status *</label>
          <select id="episode-status" name="status" required onchange="toggleDeathFields('episode')">
            <option value="active">Active</option>
            <option value="monitoring">Monitoring</option>
            <option value="resolved">Resolved</option>
            <option value="deceased">Deceased</option>
          </select>
        </div>

        <!-- Death-related fields (hidden by default) -->
        <div id="episode-death-fields" class="death-fields" style="display: none;">
          <div class="form-group">
            <label for="episode-date-of-death">Date of Death *</label>
            <input type="date" id="episode-date-of-death" name="date_of_death">
          </div>

          <div class="form-group">
            <label for="episode-reason-for-death">Reason for Death *</label>
            <textarea id="episode-reason-for-death" name="reason_for_death" rows="3" placeholder="Describe the cause or circumstances of death..."></textarea>
          </div>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <div class="form-actions">
          <button type="cancel" onclick="closeNewEpisodeModal()">Cancel</button>
          <button type="submit" class="btn-primary">Create Episode</button>
        </div>
        <div id="episode-feedback" class="feedback"></div>
      </form>
    </div>
  </div>

  <!-- Episode Detail Modal -->
  <div id="episode-detail-modal" class="modal">
    <div class="modal-content large">
      <span class="close-modal" onclick="closeEpisodeDetailModal()">✖</span>
      <div id="episode-detail-content">
        <!-- Episode details will be loaded here -->
      </div>
    </div>
  </div>

  <!-- Update Episode Status Modal -->
  <div id="update-status-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeUpdateStatusModal()">✖</span>
      <h2>Update Episode Status</h2>
      <form id="update-status-form">
        <input type="hidden" id="status-episode-id" name="episode_id">

        <div class="form-group">
          <label for="episode-new-status">New Status *</label>
          <select id="episode-new-status" name="status" required onchange="toggleDeathFields('status')">
            <option value="active">Active</option>
            <option value="monitoring">Monitoring</option>
            <option value="resolved">Resolved</option>
            <option value="deceased">Deceased</option>
          </select>
        </div>

        <!-- Death-related fields (hidden by default) -->
        <div id="status-death-fields" class="death-fields" style="display: none;">
          <div class="form-group">
            <label for="status-date-of-death">Date of Death *</label>
            <input type="date" id="status-date-of-death" name="date_of_death">
          </div>

          <div class="form-group">
            <label for="status-reason-for-death">Reason for Death *</label>
            <textarea id="status-reason-for-death" name="reason_for_death" rows="3" placeholder="Describe the cause or circumstances of death..."></textarea>
          </div>
        </div>

        <div class="form-group">
          <label for="status-notes">Status Change Notes (Optional)</label>
          <textarea id="status-notes" name="notes" rows="3" placeholder="Add any notes about this status change..."></textarea>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <div class="form-actions">
          <button type="cancel" onclick="closeUpdateStatusModal()">Cancel</button>
          <button type="submit" class="btn-primary">Update Status</button>
        </div>
        <div id="status-feedback" class="feedback"></div>
      </form>
    </div>
  </div>

  <!-- New Intervention Modal -->
  <div id="new-intervention-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeNewInterventionModal()">✖</span>
      <h2>New Intervention Log</h2>
      <form id="new-intervention-form">
        <input type="hidden" id="intervention-episode-id" name="episode_id">
        
        <div class="form-group">
          <label for="intervention-date">Date & Time *</label>
          <input type="datetime-local" id="intervention-date" name="intervention_date" required>
        </div>
        
        <div class="form-group">
          <label for="intervention-type">Intervention Type *</label>
          <select id="intervention-type" name="intervention_type" required onchange="toggleDeathFields('intervention')">
            <option value="">Select type...</option>
            <option value="examination">Examination</option>
            <option value="medication">Medication</option>
            <option value="treatment">Treatment</option>
            <option value="surgery">Surgery</option>
            <option value="therapy">Therapy</option>
            <option value="monitoring">Monitoring</option>
            <option value="euthanasia">Euthanasia</option>
            <option value="other">Other</option>
          </select>
        </div>

        <!-- Death-related fields (hidden by default) -->
        <div id="intervention-death-fields" class="death-fields" style="display: none;">
          <div class="form-group">
            <label for="intervention-date-of-death">Date of Death *</label>
            <input type="date" id="intervention-date-of-death" name="date_of_death">
          </div>

          <div class="form-group">
            <label for="intervention-reason-for-death">Reason for Death *</label>
            <textarea id="intervention-reason-for-death" name="reason_for_death" rows="3" placeholder="Describe the cause or circumstances of death..."></textarea>
          </div>
        </div>
        
        <div class="form-group">
          <label for="intervention-description">Description *</label>
          <textarea id="intervention-description" name="description" required rows="4" placeholder="Describe what was done, observations, medications given, etc..."></textarea>
        </div>

        <div class="form-group">
          <label for="intervention-outcome">Outcome/Response</label>
          <textarea id="intervention-outcome" name="outcome" rows="3" placeholder="How did the animal respond? Any changes observed?"></textarea>
        </div>
        
        <div class="form-group">
          <label for="intervention-next-steps">Next Steps</label>
          <textarea id="intervention-next-steps" name="next_steps" rows="2" placeholder="What should be done next?"></textarea>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <div class="form-actions">
          <button type="cancel" onclick="closeNewInterventionModal()">Cancel</button>
          <button type="submit" class="btn-primary">Add Intervention</button>
        </div>
        <div id="intervention-feedback" class="feedback"></div>
      </form>
    </div>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <!-- Global Functions -->
  <script>
    function toggleMenu() {
      console.log('toggleMenu called');
      const sideMenu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');

      if (!sideMenu || !overlay) {
        console.error('Menu elements not found:', { sideMenu, overlay });
        return;
      }

      const isOpen = sideMenu.classList.contains('open');
      console.log('Menu is currently:', isOpen ? 'open' : 'closed');

      if (isOpen) {
        sideMenu.classList.remove('open');
        overlay.classList.remove('active');
        console.log('Menu closed');
      } else {
        sideMenu.classList.add('open');
        overlay.classList.add('active');
        console.log('Menu opened');
      }
    }
    
    function openNewEpisodeModal() {
      // Set default date to today
      document.getElementById('episode-date').value = new Date().toISOString().split('T')[0];

      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('new-episode-form');

      document.getElementById('new-episode-modal').style.display = 'flex';
    }
    
    function closeNewEpisodeModal() {
      document.getElementById('new-episode-modal').style.display = 'none';
      document.getElementById('new-episode-form').reset();
    }
    
    function closeEpisodeDetailModal() {
      document.getElementById('episode-detail-modal').style.display = 'none';
    }
    
    window.openNewInterventionModal = function(episodeId) {
      document.getElementById('intervention-episode-id').value = episodeId;
      // Set default date to now
      const now = new Date();
      now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
      document.getElementById('intervention-date').value = now.toISOString().slice(0, 16);

      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('new-intervention-form');

      document.getElementById('new-intervention-modal').style.display = 'flex';
    }
    
    function closeNewInterventionModal() {
      document.getElementById('new-intervention-modal').style.display = 'none';
      document.getElementById('new-intervention-form').reset();
    }

    function openUpdateStatusModal(episodeId, currentStatus) {
      document.getElementById('status-episode-id').value = episodeId;
      document.getElementById('episode-new-status').value = currentStatus;

      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('update-status-form');

      document.getElementById('update-status-modal').style.display = 'flex';
    }

    function closeUpdateStatusModal() {
      document.getElementById('update-status-modal').style.display = 'none';
      document.getElementById('update-status-form').reset();
    }

    // Toggle death fields based on status/intervention type selection
    function toggleDeathFields(context) {
      let statusValue, deathFieldsId;

      if (context === 'episode') {
        statusValue = document.getElementById('episode-status').value;
        deathFieldsId = 'episode-death-fields';
      } else if (context === 'status') {
        statusValue = document.getElementById('episode-new-status').value;
        deathFieldsId = 'status-death-fields';
      } else if (context === 'intervention') {
        statusValue = document.getElementById('intervention-type').value;
        deathFieldsId = 'intervention-death-fields';
      }

      const deathFields = document.getElementById(deathFieldsId);
      const isDeathRelated = (context === 'intervention' && statusValue === 'euthanasia') ||
                            (context !== 'intervention' && statusValue === 'deceased');

      if (isDeathRelated) {
        deathFields.style.display = 'block';
        // Make death fields required
        const dateField = deathFields.querySelector('input[type="date"]');
        const reasonField = deathFields.querySelector('textarea');
        if (dateField) dateField.required = true;
        if (reasonField) reasonField.required = true;
      } else {
        deathFields.style.display = 'none';
        // Remove required attribute
        const dateField = deathFields.querySelector('input[type="date"]');
        const reasonField = deathFields.querySelector('textarea');
        if (dateField) {
          dateField.required = false;
          dateField.value = '';
        }
        if (reasonField) {
          reasonField.required = false;
          reasonField.value = '';
        }
      }
    }
  </script>

  <script src="auth-utils.js"></script>
  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    const urlParams = new URLSearchParams(window.location.search);
    const animalId = urlParams.get('id');
    let currentAnimal = null;

    async function initializeApp() {
      console.log('Starting medical tracker initialization...');
      console.log('Current URL:', window.location.href);
      console.log('Animal ID from URL:', animalId);

      // Check authentication
      console.log('Checking authentication...');
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        console.log('User not authenticated, redirecting to login');
        window.location.href = 'login.html';
        return;
      }

      console.log('✅ User authenticated:', user.email);

      if (!animalId) {
        console.error('❌ No animal ID provided in URL');
        document.body.innerHTML = '<h2>No animal ID provided. Please access this page from an animal record.</h2>';
        return;
      }

      // Test database connection
      console.log('🔄 Testing database connection...');
      try {
        const { data: testData, error: testError } = await supabase
          .from('medical_episodes')
          .select('id')
          .limit(1);

        if (testError) {
          console.error('❌ Database connection test failed:', testError);
          document.body.innerHTML = '<h2>Database connection error. Please check if medical tracker tables exist.</h2>';
          return;
        }
        console.log('✅ Database connection test passed');
      } catch (error) {
        console.error('❌ Database connection test error:', error);
        document.body.innerHTML = '<h2>Database connection error. Please try again.</h2>';
        return;
      }

      try {
        await loadAnimal();
        await loadEpisodes();
        console.log('✅ Medical tracker initialization complete');
      } catch (error) {
        console.error('❌ Error during initialization:', error);
        document.body.innerHTML = `<h2>Error loading medical tracker: ${error.message}</h2>`;
      }
    }

    // Load animal data
    async function loadAnimal() {
      console.log('🔄 Fetching animal with ID:', animalId);

      try {
        const { data: animals, error } = await supabase
          .from('animals')
          .select('*')
          .eq('id', animalId);

        if (error) {
          console.error('❌ Supabase error fetching animal:', error);
          throw new Error(`Database error: ${error.message}`);
        }

        console.log('📊 Raw animal data response:', animals);

        if (!animals || animals.length === 0) {
          console.log('❌ Animal not found in database');
          throw new Error('Animal not found');
        }

        const animal = animals[0];
        console.log('✅ Animal loaded successfully:', animal.name);
        currentAnimal = animal;

        // Update UI elements
        const nameEl = document.getElementById('animal-name');
        const speciesEl = document.getElementById('animal-species');
        const statusEl = document.getElementById('animal-status');
        const photoEl = document.getElementById('animal-photo');
        const backLinkEl = document.getElementById('back-to-animal');

        if (nameEl) nameEl.textContent = animal.name;
        if (speciesEl) speciesEl.textContent = animal.species;
        if (statusEl) statusEl.textContent = animal.status;
        if (photoEl) photoEl.src = animal.photo_url || 'assets/images/placeholder.jpg';
        if (backLinkEl) backLinkEl.href = `animal.html?id=${animal.id}`;

        // Update page title
        document.title = `Medical Tracker - ${animal.name}`;

        // Show deceased alert if animal is deceased
        const deceasedAlert = document.getElementById('deceased-alert');
        if (animal.status === 'Deceased' && animal.date_of_death && animal.reason_for_death) {
          const deathDate = new Date(animal.date_of_death).toLocaleDateString('en-GB', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
          document.getElementById('death-date').textContent = deathDate;
          document.getElementById('death-reason').textContent = animal.reason_for_death;
          deceasedAlert.style.display = 'block';
        } else {
          deceasedAlert.style.display = 'none';
        }

        console.log('✅ Animal UI updated successfully');
      } catch (error) {
        console.error('❌ Error in loadAnimal:', error);
        throw error; // Re-throw to be caught by initializeApp
      }
    }

    // Load medical episodes
    async function loadEpisodes() {
      console.log('🔄 Fetching medical episodes for animal:', animalId);

      try {
        const { data: episodes, error } = await supabase
          .from('medical_episodes')
          .select('*')
          .eq('animal_id', animalId)
          .order('date_discovered', { ascending: false });

        if (error) {
          console.error('❌ Supabase error fetching episodes:', error);
          throw new Error(`Database error: ${error.message}`);
        }

        console.log('📊 Raw episodes data response:', episodes);
        console.log('✅ Episodes loaded:', episodes.length);

        const container = document.getElementById('episodes-container');
        if (!container) {
          console.error('❌ Episodes container element not found');
          return;
        }

        if (episodes.length === 0) {
          console.log('ℹ️ No episodes found for this animal');
          container.innerHTML = '<p class="no-episodes">No medical episodes recorded yet.</p>';
          return;
        }

        // Fetch creator information for episodes that have created_by
        for (let episode of episodes) {
          if (episode.created_by) {
            try {
              const { data: creator, error: creatorError } = await supabase
                .from('staff_volunteers')
                .select('first_name, last_name, role')
                .eq('user_id', episode.created_by)
                .single();

              if (!creatorError && creator) {
                episode.created_by_profile = creator;
              }
            } catch (err) {
              console.log('Could not fetch creator info for episode:', episode.id);
            }
          }
        }

        console.log('🔄 Rendering episodes...');
        container.innerHTML = episodes.map(episode => `
          <div class="episode-card" onclick="openEpisodeDetail('${episode.id}')">
            <div class="episode-header">
              <h3 class="episode-title">${episode.title}</h3>
              <span class="episode-status ${episode.status}">${episode.status}</span>
            </div>
            <div class="episode-meta">
              <span>Type: ${episode.type}</span>
              <span>Severity: <span class="severity-badge ${episode.severity}">${episode.severity}</span></span>
              <span>Date: ${new Date(episode.date_discovered).toLocaleDateString()}</span>
              ${episode.created_by_profile ? `<span>Created by: ${episode.created_by_profile.first_name} ${episode.created_by_profile.last_name} (${episode.created_by_profile.role})</span>` : ''}
            </div>
            <div class="episode-description">${episode.description}</div>
            <div class="episode-stats">
              <span id="intervention-count-${episode.id}">Loading interventions...</span>
            </div>
          </div>
        `).join('');

        console.log('✅ Episodes rendered successfully');

        // Load intervention counts for each episode
        console.log('🔄 Loading intervention counts...');
        episodes.forEach(episode => loadInterventionCount(episode.id));
      } catch (error) {
        console.error('❌ Error in loadEpisodes:', error);
        const container = document.getElementById('episodes-container');
        if (container) {
          container.innerHTML = '<p class="no-episodes">Error loading episodes. Please try again.</p>';
        }
        throw error; // Re-throw to be caught by initializeApp
      }
    }

    // Load intervention count for an episode
    async function loadInterventionCount(episodeId) {
      const { data: interventions, error } = await supabase
        .from('medical_interventions')
        .select('id')
        .eq('episode_id', episodeId);

      if (error) {
        console.error('Error loading intervention count:', error);
        return;
      }

      const countElement = document.getElementById(`intervention-count-${episodeId}`);
      if (countElement) {
        countElement.textContent = `${interventions.length} intervention${interventions.length !== 1 ? 's' : ''}`;
      }
    }

    // Open episode detail modal
    window.openEpisodeDetail = async function(episodeId) {
      try {
        // Load episode details
        const { data: episodes, error: episodeError } = await supabase
          .from('medical_episodes')
          .select('*')
          .eq('id', episodeId);

        if (episodeError || !episodes || episodes.length === 0) {
          console.error('Error loading episode:', episodeError);
          return;
        }

        const episode = episodes[0];

        // Load interventions
        const { data: interventions, error: interventionsError } = await supabase
          .from('medical_interventions')
          .select('*')
          .eq('episode_id', episodeId)
          .order('intervention_date', { ascending: false });

        if (interventionsError) {
          console.error('Error loading interventions:', interventionsError);
          return;
        }

        // Build episode detail content
        const content = document.getElementById('episode-detail-content');
        content.innerHTML = `
          <div class="episode-detail-header">
            <div>
              <h2 class="episode-detail-title">${episode.title}</h2>
              <div class="episode-meta">
                <span>Type: ${episode.type}</span>
                <span>Severity: <span class="severity-badge ${episode.severity}">${episode.severity}</span></span>
                <span>Status: <span class="episode-status ${episode.status}">${episode.status}</span></span>
                <span>Date Discovered: ${new Date(episode.date_discovered).toLocaleDateString()}</span>
              </div>
              <div class="episode-description">${episode.description}</div>
            </div>
            <div class="episode-actions">
              <button class="btn-secondary" onclick="openUpdateStatusModal('${episode.id}', '${episode.status}')">
                <span class="material-icons">edit</span>
                Update Status
              </button>
            </div>
          </div>

          <div class="interventions-section">
            <div class="interventions-header">
              <h3>Interventions (${interventions.length})</h3>
              <button class="btn-primary" onclick="openNewInterventionModal('${episode.id}')">
                <span class="material-icons">add</span>
                Add Intervention
              </button>
            </div>
            <div class="interventions-list">
              ${interventions.length === 0 ? '<p>No interventions recorded yet.</p>' :
                interventions.map(intervention => `
                  <div class="intervention-card">
                    <div class="intervention-header">
                      <span class="intervention-type">${intervention.intervention_type}</span>
                      <span class="intervention-date">${new Date(intervention.intervention_date).toLocaleString()}</span>
                    </div>
                    <div class="intervention-description">${intervention.description}</div>
                    ${intervention.staff_member || intervention.outcome || intervention.next_steps ? `
                      <div class="intervention-details">
                        ${intervention.staff_member ? `<div class="intervention-detail"><strong>Staff:</strong> ${intervention.staff_member}</div>` : ''}
                        ${intervention.outcome ? `<div class="intervention-detail"><strong>Outcome:</strong> ${intervention.outcome}</div>` : ''}
                        ${intervention.next_steps ? `<div class="intervention-detail"><strong>Next Steps:</strong> ${intervention.next_steps}</div>` : ''}
                      </div>
                    ` : ''}
                  </div>
                `).join('')
              }
            </div>
          </div>
        `;

        document.getElementById('episode-detail-modal').style.display = 'flex';
      } catch (error) {
        console.error('Error loading episode details:', error);
        alert('Error loading episode details. Please try again.');
      }
    };

    // Function to update animal death record
    async function updateAnimalDeathRecord(dateOfDeath, reasonForDeath) {
      try {
        const { error } = await supabase
          .from('animals')
          .update({
            status: 'Deceased',
            date_of_death: dateOfDeath,
            reason_for_death: reasonForDeath,
            updated_by: (await supabase.auth.getUser()).data.user?.id
          })
          .eq('id', animalId);

        if (error) {
          console.error('Error updating animal death record:', error);
          throw error;
        }

        console.log('Animal death record updated successfully');
      } catch (error) {
        console.error('Failed to update animal death record:', error);
        throw error;
      }
    }

    // Handle new episode form submission
    document.getElementById('new-episode-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      const episodeData = {
        animal_id: animalId,
        title: formData.get('title'),
        type: formData.get('type'),
        severity: formData.get('severity'),
        description: formData.get('description'),
        date_discovered: formData.get('date_discovered'),
        status: formData.get('status'),
        created_at: new Date().toISOString()
      };

      // Add user attribution
      const episodeDataWithAttribution = window.authUtils.addUserAttribution(episodeData, false);

      const { data, error } = await supabase
        .from('medical_episodes')
        .insert([episodeDataWithAttribution]);

      if (error) {
        document.getElementById('episode-feedback').innerHTML = '<div class="feedback error">❌ Error creating episode.</div>';
        console.error('Error creating episode:', error);
      } else {
        // If episode status is deceased, update animal record
        if (formData.get('status') === 'deceased') {
          const dateOfDeath = formData.get('date_of_death');
          const reasonForDeath = formData.get('reason_for_death');

          if (dateOfDeath && reasonForDeath) {
            await updateAnimalDeathRecord(dateOfDeath, reasonForDeath);
          }
        }

        document.getElementById('episode-feedback').innerHTML = '<div class="feedback success">✅ Episode created successfully.</div>';
        e.target.reset();
        setTimeout(() => {
          closeNewEpisodeModal();
          loadEpisodes(); // Reload episodes
          loadAnimal(); // Reload animal data to show updated status
        }, 1500);
      }
    });

    // Handle update status form submission
    document.getElementById('update-status-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      try {
        const formData = new FormData(e.target);
        const episodeId = formData.get('episode_id');
        const newStatus = formData.get('status');
        const notes = formData.get('notes');

        const { error: updateError } = await supabase
          .from('medical_episodes')
          .update({
            status: newStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', episodeId);

        if (updateError) {
          document.getElementById('status-feedback').innerHTML = '<div class="feedback error">❌ Error updating status.</div>';
          console.error('Error updating status:', updateError);
          return;
        }

        // If status is changed to deceased, update animal record
        if (newStatus === 'deceased') {
          const dateOfDeath = formData.get('date_of_death');
          const reasonForDeath = formData.get('reason_for_death');

          if (dateOfDeath && reasonForDeath) {
            await updateAnimalDeathRecord(dateOfDeath, reasonForDeath);
          }
        }

        document.getElementById('status-feedback').innerHTML = '<div class="feedback success">✅ Status updated successfully.</div>';

        // If notes were provided, add them as an intervention
        if (notes && notes.trim()) {
          // Get current user info for staff_member field
          const userInfo = window.authUtils.getCurrentUserInfo();

          const interventionData = {
            episode_id: episodeId,
            intervention_date: new Date().toISOString(),
            intervention_type: 'monitoring',
            description: `Status changed to "${newStatus}". ${notes}`,
            staff_member: userInfo ? userInfo.name : 'Unknown Staff',
            outcome: null,
            next_steps: null,
            created_at: new Date().toISOString()
          };

          // Add user attribution
          const interventionDataWithAttribution = window.authUtils.addUserAttribution(interventionData, false);

          await supabase
            .from('medical_interventions')
            .insert([interventionDataWithAttribution]);
        }

        setTimeout(() => {
          closeUpdateStatusModal();
          loadEpisodes(); // Reload episodes list
          loadAnimal(); // Reload animal data to show updated status
          // Refresh the episode detail if it's open
          if (document.getElementById('episode-detail-modal').style.display === 'flex') {
            openEpisodeDetail(episodeId);
          }
        }, 1500);

      } catch (error) {
        document.getElementById('status-feedback').innerHTML = '<div class="feedback error">❌ Error updating status.</div>';
        console.error('Error:', error);
      }
    });

    // Handle new intervention form submission
    document.getElementById('new-intervention-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      // Get current user info for staff_member field
      const userInfo = window.authUtils.getCurrentUserInfo();

      const interventionData = {
        episode_id: formData.get('episode_id'),
        intervention_date: formData.get('intervention_date'),
        intervention_type: formData.get('intervention_type'),
        description: formData.get('description'),
        staff_member: userInfo ? userInfo.name : 'Unknown Staff',
        outcome: formData.get('outcome') || null,
        next_steps: formData.get('next_steps') || null,
        created_at: new Date().toISOString()
      };

      // Add user attribution
      const interventionDataWithAttribution = window.authUtils.addUserAttribution(interventionData, false);

      const { data, error } = await supabase
        .from('medical_interventions')
        .insert([interventionDataWithAttribution]);

      if (error) {
        document.getElementById('intervention-feedback').innerHTML = '<div class="feedback error">❌ Error adding intervention.</div>';
        console.error('Error adding intervention:', error);
      } else {
        // If intervention type is euthanasia, update animal record and episode status
        if (formData.get('intervention_type') === 'euthanasia') {
          const dateOfDeath = formData.get('date_of_death');
          const reasonForDeath = formData.get('reason_for_death');
          const episodeId = formData.get('episode_id');

          if (dateOfDeath && reasonForDeath) {
            // Update animal death record
            await updateAnimalDeathRecord(dateOfDeath, reasonForDeath);

            // Update episode status to deceased
            await supabase
              .from('medical_episodes')
              .update({
                status: 'deceased',
                updated_at: new Date().toISOString()
              })
              .eq('id', episodeId);
          }
        }

        document.getElementById('intervention-feedback').innerHTML = '<div class="feedback success">✅ Intervention added successfully.</div>';
        e.target.reset();
        setTimeout(() => {
          closeNewInterventionModal();
          // Refresh the episode detail if it's open
          const episodeId = formData.get('episode_id');
          if (document.getElementById('episode-detail-modal').style.display === 'flex') {
            openEpisodeDetail(episodeId);
          }
          loadEpisodes(); // Reload episodes to update counts
          loadAnimal(); // Reload animal data to show updated status
        }, 1500);
      }
    });

    // Initialize page
    initializeApp();
  </script>
</body>
</html>
