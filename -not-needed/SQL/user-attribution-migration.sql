-- User Attribution Migration Script
-- Add created_by and updated_by fields to tables that don't have them

-- Add user attribution to daily_logs table
ALTER TABLE daily_logs 
ADD COLUMN created_by UUID REFERENCES auth.users(id),
ADD COLUMN updated_by UUID REFERENCES auth.users(id);

-- Add user attribution to staff_volunteers table
ALTER TABLE staff_volunteers 
ADD COLUMN created_by UUID REFERENCES auth.users(id),
ADD COLUMN updated_by UUID REFERENCES auth.users(id);

-- Add user attribution to emergency_contacts table
ALTER TABLE emergency_contacts 
ADD COLUMN created_by UUID REFERENCES auth.users(id),
ADD COLUMN updated_by UUID REFERENCES auth.users(id);

-- Create audit triggers for tables that don't have them yet
CREATE TRIGGER audit_daily_logs_trigger
  AFTER INSERT OR UPDATE OR DELETE ON daily_logs
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

CREATE TRIGGER audit_emergency_contacts_trigger
  AFTER INSERT OR UPDATE OR DELETE ON emergency_contacts
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

-- Note: staff_volunteers and documents tables may already have audit triggers
-- Check existing triggers before running:
-- SELECT * FROM information_schema.triggers WHERE event_object_table IN ('staff_volunteers', 'documents');
