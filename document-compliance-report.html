<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Document Compliance Report – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
  <style>
    /* Report-specific styles */
    .report-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      background: white;
      min-height: 100vh;
    }

    .report-header {
      text-align: center;
      margin-bottom: 2rem;
      border-bottom: 2px solid #2c3e50;
      padding-bottom: 1rem;
    }

    .report-title {
      color: #2c3e50;
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    .report-subtitle {
      color: #666;
      font-size: 1.1rem;
    }

    .summary-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 1.5rem;
      text-align: center;
      border-left: 4px solid #4285a4;
    }

    .summary-card.policy {
      border-left-color: #28a745;
    }

    .summary-card.emergency {
      border-left-color: #dc3545;
    }

    .summary-card.risk-assessment {
      border-left-color: #ffc107;
    }

    .summary-card.medical {
      border-left-color: #17a2b8;
    }

    .summary-card.other {
      border-left-color: #6c757d;
    }

    .summary-number {
      font-size: 2rem;
      font-weight: bold;
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }

    .summary-label {
      color: #666;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .report-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 2rem;
      font-size: 0.9rem;
    }

    .report-table th {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem 0.75rem;
      text-align: left;
      font-weight: 600;
      border: 1px solid #ddd;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .report-table td {
      padding: 0.75rem;
      border: 1px solid #ddd;
      vertical-align: top;
    }

    .report-table tbody tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    .report-table tbody tr:hover {
      background-color: #e9ecef;
    }

    /* Review date color coding */
    .review-date-red {
      background-color: #ffebee !important;
      color: #c62828;
      font-weight: bold;
    }

    .review-date-orange {
      background-color: #fff3e0 !important;
      color: #ef6c00;
      font-weight: bold;
    }

    .review-date-normal {
      color: #2c3e50;
    }

    .print-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      display: flex;
      gap: 10px;
      z-index: 1000;
    }

    .print-btn {
      background: #4285a4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 0.9rem;
    }

    .print-btn:hover {
      background: #357a96;
    }

    /* Print styles */
    @media print {
      .print-controls {
        display: none !important;
      }

      .report-container {
        padding: 0;
        max-width: none;
      }

      .report-table th {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

      .review-date-red {
        background-color: #ffebee !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

      .review-date-orange {
        background-color: #fff3e0 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }

      .summary-card {
        break-inside: avoid;
      }

      .report-table {
        break-inside: auto;
      }

      .report-table thead {
        display: table-header-group;
      }

      .report-table tbody tr {
        break-inside: avoid;
      }
    }

    @media (max-width: 768px) {
      .report-container {
        padding: 1rem;
      }

      .summary-section {
        grid-template-columns: 1fr;
      }

      .report-table {
        font-size: 0.8rem;
      }

      .report-table th,
      .report-table td {
        padding: 0.5rem;
      }

      .print-controls {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 1rem;
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="print-controls">
    <button class="print-btn" onclick="window.print()">
      <span class="material-icons">print</span>
      Print Report
    </button>
    <button class="print-btn" onclick="window.print()">
      <span class="material-icons">picture_as_pdf</span>
      Save as PDF
    </button>
  </div>

  <div class="report-container">
    <div class="report-header">
      <h1 class="report-title">Document Compliance Report</h1>
      <p class="report-subtitle" id="report-subtitle">Generated on <span id="report-date"></span></p>
    </div>

    <!-- Summary Section -->
    <div class="summary-section" id="summary-section">
      <div class="summary-card">
        <div class="summary-number" id="total-documents">0</div>
        <div class="summary-label">Total Documents</div>
      </div>
      <div class="summary-card policy">
        <div class="summary-number" id="policy-count">0</div>
        <div class="summary-label">Policy Documents</div>
      </div>
      <div class="summary-card emergency">
        <div class="summary-number" id="emergency-count">0</div>
        <div class="summary-label">Emergency Procedures</div>
      </div>
      <div class="summary-card risk-assessment">
        <div class="summary-number" id="risk-assessment-count">0</div>
        <div class="summary-label">Risk Assessments</div>
      </div>
      <div class="summary-card medical">
        <div class="summary-number" id="medical-count">0</div>
        <div class="summary-label">Medical Documents</div>
      </div>
      <div class="summary-card other">
        <div class="summary-number" id="other-count">0</div>
        <div class="summary-label">Other Documents</div>
      </div>
    </div>

    <!-- Documents Table -->
    <table class="report-table" id="documents-table">
      <thead>
        <tr id="table-headers">
          <!-- Headers will be dynamically generated -->
        </tr>
      </thead>
      <tbody id="table-body">
        <!-- Data will be dynamically generated -->
      </tbody>
    </table>
  </div>

  <script type="module">
    // Import Supabase
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    // Initialize Supabase client
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filterType = urlParams.get('filter') || 'all';
    const selectedColumns = urlParams.get('columns') ? urlParams.get('columns').split(',') : ['original_filename', 'category', 'uploaded_at', 'review_date'];

    // Set report date
    document.getElementById('report-date').textContent = new Date().toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Column mappings
    const columnMappings = {
      'original_filename': 'Document Name',
      'category': 'Document Type',
      'uploaded_at': 'Creation Date',
      'review_date': 'Review Date',
      'file_size': 'File Size',
      'created_at': 'Document Created Date'
    };

    // Load and display documents
    async function loadDocuments() {
      try {
        // Check authentication
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
          alert('Please log in to view this report.');
          window.close();
          return;
        }

        // Build query
        let query = supabase
          .from('documents')
          .select('*')
          .order('uploaded_at', { ascending: false });

        // Apply filters
        const today = new Date();
        const oneMonthFromNow = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000));
        const threeMonthsFromNow = new Date(today.getTime() + (90 * 24 * 60 * 60 * 1000));

        switch (filterType) {
          case 'expiring-1':
            query = query.lte('review_date', oneMonthFromNow.toISOString().split('T')[0]);
            break;
          case 'expiring-3':
            query = query.lte('review_date', threeMonthsFromNow.toISOString().split('T')[0]);
            break;
          case 'overdue':
            query = query.lt('review_date', today.toISOString().split('T')[0]);
            break;
        }

        const { data: documents, error } = await query;

        if (error) {
          console.error('Error loading documents:', error);
          alert('Error loading documents. Please try again.');
          return;
        }

        // Update summary
        updateSummary(documents);

        // Generate table
        generateTable(documents);

      } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while loading the report.');
      }
    }

    // Update summary section
    function updateSummary(documents) {
      const summary = {
        total: documents.length,
        policy: documents.filter(doc => doc.category === 'policy').length,
        emergency: documents.filter(doc => doc.category === 'emergency').length,
        'risk-assessment': documents.filter(doc => doc.category === 'risk-assessment').length,
        medical: documents.filter(doc => doc.category === 'medical').length,
        other: documents.filter(doc => doc.category === 'other').length
      };

      document.getElementById('total-documents').textContent = summary.total;
      document.getElementById('policy-count').textContent = summary.policy;
      document.getElementById('emergency-count').textContent = summary.emergency;
      document.getElementById('risk-assessment-count').textContent = summary['risk-assessment'];
      document.getElementById('medical-count').textContent = summary.medical;
      document.getElementById('other-count').textContent = summary.other;

      // Update subtitle with filter info
      const subtitle = document.getElementById('report-subtitle');
      let filterText = '';
      switch (filterType) {
        case 'expiring-1':
          filterText = ' - Documents Expiring in 1 Month';
          break;
        case 'expiring-3':
          filterText = ' - Documents Expiring in 3 Months';
          break;
        case 'overdue':
          filterText = ' - Overdue Documents';
          break;
        default:
          filterText = ' - All Documents';
      }
      subtitle.innerHTML = `Generated on <span id="report-date">${new Date().toLocaleDateString('en-GB', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })}</span>${filterText}`;
    }

    // Generate table headers and data
    function generateTable(documents) {
      const tableHeaders = document.getElementById('table-headers');
      const tableBody = document.getElementById('table-body');

      // Clear existing content
      tableHeaders.innerHTML = '';
      tableBody.innerHTML = '';

      // Generate headers
      selectedColumns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = columnMappings[column] || column;
        tableHeaders.appendChild(th);
      });

      // Generate rows
      documents.forEach(document => {
        const row = document.createElement('tr');

        selectedColumns.forEach(column => {
          const td = document.createElement('td');
          let value = document[column];

          // Format values based on column type
          switch (column) {
            case 'uploaded_at':
            case 'created_at':
              value = value ? new Date(value).toLocaleDateString('en-GB') : 'N/A';
              break;
            case 'review_date':
              if (value) {
                const reviewDate = new Date(value);
                const today = new Date();
                const oneMonthFromNow = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000));
                const threeMonthsFromNow = new Date(today.getTime() + (90 * 24 * 60 * 60 * 1000));

                value = reviewDate.toLocaleDateString('en-GB');

                // Apply color coding
                if (reviewDate < today) {
                  td.classList.add('review-date-red');
                } else if (reviewDate <= oneMonthFromNow) {
                  td.classList.add('review-date-red');
                } else if (reviewDate <= threeMonthsFromNow) {
                  td.classList.add('review-date-orange');
                } else {
                  td.classList.add('review-date-normal');
                }
              } else {
                // If no review_date, calculate from uploaded_at + 1 year
                if (document.uploaded_at) {
                  const uploadDate = new Date(document.uploaded_at);
                  const reviewDate = new Date(uploadDate);
                  reviewDate.setFullYear(reviewDate.getFullYear() + 1);

                  const today = new Date();
                  const oneMonthFromNow = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000));
                  const threeMonthsFromNow = new Date(today.getTime() + (90 * 24 * 60 * 60 * 1000));

                  value = reviewDate.toLocaleDateString('en-GB');

                  // Apply color coding
                  if (reviewDate < today) {
                    td.classList.add('review-date-red');
                  } else if (reviewDate <= oneMonthFromNow) {
                    td.classList.add('review-date-red');
                  } else if (reviewDate <= threeMonthsFromNow) {
                    td.classList.add('review-date-orange');
                  } else {
                    td.classList.add('review-date-normal');
                  }
                } else {
                  value = 'N/A';
                }
              }
              break;
            case 'file_size':
              value = value ? formatFileSize(value) : 'N/A';
              break;
            case 'category':
              value = formatCategory(value);
              break;
            default:
              value = value || 'N/A';
          }

          td.textContent = value;
          row.appendChild(td);
        });

        tableBody.appendChild(row);
      });
    }

    // Helper function to format file size
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Helper function to format category
    function formatCategory(category) {
      const categoryMap = {
        'policy': 'Policy Documents',
        'emergency': 'Emergency Procedures',
        'risk-assessment': 'Risk Assessments',
        'medical': 'Medical Documents',
        'other': 'Other/Miscellaneous'
      };
      return categoryMap[category] || category;
    }

    // Initialize the report
    loadDocuments();
  </script>
</body>
</html>