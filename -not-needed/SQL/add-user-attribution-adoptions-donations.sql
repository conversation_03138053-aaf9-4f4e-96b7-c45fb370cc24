-- Add User Attribution to Adoptions & Donations Table
-- Migration script to add created_by and updated_by columns

-- Add user attribution columns to adoptions_donations table
ALTER TABLE adoptions_donations 
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES auth.users(id);

-- Create audit trigger for adoptions_donations table
CREATE OR REPLACE TRIGGER audit_adoptions_donations_trigger
  AFTER INSERT OR UPDATE OR DELETE ON adoptions_donations
  FOR EACH ROW EXECUTE FUNCTION log_user_action();

-- Update existing records to set created_by to current user (optional)
-- Uncomment the line below if you want to attribute existing records to the current user
-- UPDATE adoptions_donations SET created_by = auth.uid() WHERE created_by IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN adoptions_donations.created_by IS 'User who created this donation/adoption record';
COMMENT ON COLUMN adoptions_donations.updated_by IS 'User who last updated this donation/adoption record';
